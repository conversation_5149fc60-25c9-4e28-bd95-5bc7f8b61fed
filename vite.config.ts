import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin' // Quasar must be installed: https://quasar.dev/start/vite-plugin#introduction

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(),
            vueJsx(),
            quasar({
              sassVariables: 'src/quasar-variables.sass'
            })
           ],
  resolve: {
              alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url)),
		    "./runtimeConfig": "./runtimeConfig.browser"
              }
            }
})
