# Dependency Upgrade Plan for ExpericoderMinimalist

## Overview
This document outlines the required steps to update all dependencies in the ExpericoderMinimalist project to their latest versions. The project is a Vue 3 application using Quasar Framework, AWS Amplify, ApexCharts, and other modern web technologies.

## Current vs Target Versions

### Major Dependencies
| Package | Current | Latest | Breaking Changes |
|---------|---------|--------|------------------|
| vue | 3.2.47 | 3.5.15 | ✅ No breaking changes |
| aws-amplify | 5.3.0 | 6.15.0 | ⚠️ **MAJOR** breaking changes |
| @aws-amplify/ui-vue | 3.1.14 | 4.3.3 | ⚠️ **MAJOR** breaking changes |
| quasar | 2.11.10 | 2.18.1 | ✅ Minor updates only |
| vite | 4.3.9 | 6.3.5 | ⚠️ **MAJOR** breaking changes |
| typescript | 4.8.4 | 5.8.3 | ⚠️ **MAJOR** breaking changes |
| apexcharts | 3.39.0 | 4.7.0 | ⚠️ **MAJOR** breaking changes |
| pinia | 2.3.1 | 3.0.2 | ⚠️ **MAJOR** breaking changes |
| vue-router | 4.1.6 | 4.5.1 | ✅ Minor updates only |
| eslint | 8.38.0 | 9.27.0 | ⚠️ **MAJOR** breaking changes |

### Development Dependencies
| Package | Current | Latest | Breaking Changes |
|---------|---------|--------|------------------|
| @vitejs/plugin-vue | 4.1.0 | 5.2.4 | ⚠️ **MAJOR** breaking changes |
| @vitejs/plugin-vue-jsx | 3.0.1 | 4.2.0 | ⚠️ **MAJOR** breaking changes |
| vue-tsc | 1.2.0 | 2.2.10 | ⚠️ **MAJOR** breaking changes |
| prettier | 2.8.7 | 3.5.3 | ⚠️ **MAJOR** breaking changes |
| sass | 1.32.12 | 1.89.0 | ✅ Minor updates only |

## Phase 1: Prerequisites and Environment Setup

### 1.1 Node.js and npm Requirements
- **Current requirement**: Node.js 16+
- **New requirement**: Node.js 18+ (for AWS Amplify v6 and Vite 6)
- **Action**: Verify Node.js version is 18.x or later
- **Command**: `node --version && npm --version`

### 1.2 Backup Current State
- Create a git branch for the upgrade: `git checkout -b dependency-upgrade`
- Backup package-lock.json: `cp package-lock.json package-lock.json.backup`
- Document current working state for rollback if needed

## Phase 2: Critical Breaking Changes Analysis

### 2.1 AWS Amplify v5 → v6 (CRITICAL)
**Impact**: Complete API restructure, affects authentication and API calls

**Breaking Changes**:
- Import paths changed from `aws-amplify` to `aws-amplify/auth`, `aws-amplify/api`, etc.
- Function signatures changed from positional to named parameters
- Configuration file changed from `aws-exports.js` to `amplifyconfiguration.json`
- Auth API completely restructured

**Files Requiring Updates**:
- `src/main.ts` (lines 18-19, 49)
- `src/App.vue` (lines 102-108)
- `src/components/HelloWorld.vue` (lines 2, 6)
- `src/components/ChartList.vue` (lines 72, 98)

### 2.2 Vite v4 → v6 (MAJOR)
**Impact**: Build system changes, plugin compatibility

**Breaking Changes**:
- Plugin API changes
- Configuration structure updates
- Node.js 18+ requirement

**Files Requiring Updates**:
- `vite.config.ts` (plugin configurations)

### 2.3 TypeScript v4.8 → v5.8 (MAJOR)
**Impact**: Type checking improvements, stricter rules

**Breaking Changes**:
- Stricter null checks
- Enhanced type inference
- New compiler options

**Files Requiring Updates**:
- `tsconfig.json` (compiler options)
- Type definitions throughout codebase

### 2.4 ApexCharts v3 → v4 (MAJOR)
**Impact**: Chart rendering and API changes

**Files Requiring Updates**:
- `src/main.ts` (line 14, 48)
- Any components using ApexCharts

## Phase 3: Step-by-Step Upgrade Process

### 3.1 Pre-upgrade Preparation
```bash
# 1. Clean existing dependencies
rm -rf node_modules package-lock.json

# 2. Update Amplify CLI (if using)
npm install -g @aws-amplify/cli@latest
amplify upgrade
amplify push
```

### 3.2 Core Framework Updates (Low Risk)
```bash
# Update Vue and related packages
npm install vue@^3.5.15
npm install vue-router@^4.5.1
npm install @vue/tsconfig@^0.7.0
```

### 3.3 Build Tools Update (Medium Risk)
```bash
# Update Vite and plugins
npm install vite@^6.3.5
npm install @vitejs/plugin-vue@^5.2.4
npm install @vitejs/plugin-vue-jsx@^4.2.0
```

### 3.4 TypeScript Update (Medium Risk)
```bash
# Update TypeScript
npm install typescript@^5.8.3
npm install vue-tsc@^2.2.10
```

### 3.5 AWS Amplify Update (HIGH RISK)
```bash
# Update Amplify packages
npm install aws-amplify@^6.15.0
npm install @aws-amplify/ui-vue@^4.3.3
```

### 3.6 Other Dependencies
```bash
# Update remaining packages
npm install quasar@^2.18.1
npm install @quasar/extras@^1.17.0
npm install @quasar/vite-plugin@^1.9.0
npm install apexcharts@^4.7.0
npm install vue3-apexcharts@^1.8.0
npm install pinia@^3.0.2
npm install axios@^1.9.0
npm install eslint@^9.27.0
npm install prettier@^3.5.3
npm install sass@^1.89.0
```

## Phase 4: Code Migration Requirements

### 4.1 AWS Amplify Migration (src/main.ts)
**Before**:
```typescript
import AmplifyVue from '@aws-amplify/ui-vue'
import '@aws-amplify/ui-vue/styles.css';
app.use(AmplifyVue)
```

**After**:
```typescript
import { Authenticator } from '@aws-amplify/ui-vue'
import '@aws-amplify/ui-vue/styles.css';
app.component('Authenticator', Authenticator)
```

### 4.2 AWS Amplify Configuration (src/App.vue)
**Before**:
```typescript
import { Amplify } from 'aws-amplify'
import awsmobile from './aws-exports'
Amplify.configure(awsmobile);
```

**After**:
```typescript
import { Amplify } from 'aws-amplify'
import amplifyconfig from './amplifyconfiguration.json'
Amplify.configure(amplifyconfig);
```

### 4.3 Auth API Migration (src/components/HelloWorld.vue)
**Before**:
```typescript
import { Auth } from 'aws-amplify'
Auth.currentSession().then(data => localStorage.setItem('jwt', data.getIdToken().getJwtToken()))
```

**After**:
```typescript
import { fetchAuthSession } from 'aws-amplify/auth'
fetchAuthSession().then(session => {
  if (session.tokens?.idToken) {
    localStorage.setItem('jwt', session.tokens.idToken.toString())
  }
})
```

### 4.4 Axios API Calls (src/components/ChartList.vue)
**Before**:
```typescript
import { Auth } from 'aws-amplify';
// In method:
let config = {
  headers: {
    authorization: localStorage.getItem('jwt'),
  }
}
```

**After**:
```typescript
import { fetchAuthSession } from 'aws-amplify/auth';
// In method:
const session = await fetchAuthSession();
let config = {
  headers: {
    authorization: session.tokens?.idToken?.toString() || localStorage.getItem('jwt'),
  }
}
```

## Phase 5: Configuration File Updates

### 5.1 TypeScript Configuration (tsconfig.json)
Add new compiler options for TypeScript 5.x:
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "strict": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### 5.2 Vite Configuration (vite.config.ts)
Update for Vite 6 compatibility:
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { quasar } from '@quasar/vite-plugin'

export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    quasar({
      sassVariables: 'src/quasar-variables.sass'
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      "./runtimeConfig": "./runtimeConfig.browser"
    }
  }
})
```

## Phase 6: Testing Strategy

### 6.1 Immediate Testing After Each Phase
1. **Build Test**: `npm run build`
2. **Type Check**: `npm run type-check`
3. **Lint Check**: `npm run lint`
4. **Development Server**: `npm run dev`

### 6.2 Functional Testing
1. **Authentication Flow**: Test login/logout functionality
2. **API Calls**: Verify chart data loading
3. **Navigation**: Test Vue Router functionality
4. **UI Components**: Verify Quasar components render correctly

### 6.3 Regression Testing
1. Test all existing features
2. Verify chart rendering with ApexCharts
3. Test responsive design
4. Verify AWS integration

## Phase 7: Rollback Plan

### 7.1 If Critical Issues Arise
```bash
# Restore from backup
git checkout .
cp package-lock.json.backup package-lock.json
npm ci
```

### 7.2 Partial Rollback Strategy
- Identify which dependency caused the issue
- Revert to previous version of that specific package
- Test incrementally

## Phase 8: Post-Upgrade Tasks

### 8.1 Documentation Updates
- Update README.md with new requirements
- Document any API changes
- Update deployment scripts if needed

### 8.2 Performance Optimization
- Review bundle size changes
- Optimize imports for tree-shaking
- Update build configurations

### 8.3 Security Review
- Run `npm audit` to check for vulnerabilities
- Update any security-related configurations
- Review authentication implementation

## Estimated Timeline
- **Phase 1-2**: 2-4 hours (preparation and analysis)
- **Phase 3**: 4-6 hours (dependency updates)
- **Phase 4**: 6-8 hours (code migration)
- **Phase 5-6**: 4-6 hours (configuration and testing)
- **Total**: 16-24 hours

## Risk Assessment
- **High Risk**: AWS Amplify v6 migration (authentication changes)
- **Medium Risk**: Vite 6 and TypeScript 5 updates
- **Low Risk**: Vue 3.5, Quasar, and other minor updates

## Success Criteria
- ✅ All dependencies updated to latest versions
- ✅ Application builds without errors
- ✅ All existing functionality works
- ✅ No security vulnerabilities
- ✅ Performance maintained or improved
