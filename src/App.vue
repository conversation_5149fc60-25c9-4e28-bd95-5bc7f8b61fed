<template>
  <div class="q-pa-md">
  <q-layout view="hHh LpR fFf">

    <q-header elevated class="bg-primary text-white" height-hint="98">
      <q-toolbar>

        <q-avatar square color="primary">
          <img src="@/assets/logo-white.svg" style="height: 30px; max-width: 100px;">
        </q-avatar>

        <q-toolbar-title>
          Expericoder
        </q-toolbar-title>

        <q-btn flat @click="drawer = !drawer" round dense icon="menu" />
      </q-toolbar>

      <q-tabs align="left">
        <q-route-tab to="/facilities" label="Facilities" />
        <q-route-tab to="/about" label="About" />
      </q-tabs>
    </q-header>

    <q-drawer
        v-model="drawer"
        show-if-above

        :mini="miniState"
        @mouseover="miniState = false"
        @mouseout="miniState = true"
        mini-to-overlay

        :width="200"
        :breakpoint="500"
        bordered
        :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-3'"

        side="right"
        overlay
      >
        <q-scroll-area class="fit" :horizontal-thumb-style="{ opacity: '0' }">
          <q-list padding>
            <q-item clickable v-ripple>
              <q-item-section avatar>
                <q-icon name="person" />
              </q-item-section>

              <q-item-section>
                <div><RouterLink to="/">Profile</RouterLink></div>
              </q-item-section>
            </q-item>

            <q-item active clickable v-ripple>
              <q-item-section avatar>
                <q-icon name="bar_chart" />
              </q-item-section>

              <q-item-section>
                <div><RouterLink to="/">Data</RouterLink></div>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple>
              <q-item-section avatar>
                <q-icon name="settings" />
              </q-item-section>

              <q-item-section>
                <div><RouterLink to="/">Settings</RouterLink></div>
              </q-item-section>
            </q-item>

            <q-separator />

            <q-item clickable v-ripple>
              <q-item-section avatar>
                <q-icon name="logout" />
              </q-item-section>

              <q-item-section>
                Logout
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-drawer>

    <q-page-container>
      <RouterView  />
    </q-page-container>

  </q-layout>
</div>
</template>

<script lang="ts">
import { ref } from 'vue'
import { RouterLink, RouterView } from 'vue-router'
import { Quasar } from 'quasar'

import { Authenticator } from "@aws-amplify/ui-vue";
import "@aws-amplify/ui-vue/styles.css"

import { Amplify } from 'aws-amplify'
import amplifyconfig from './amplifyconfiguration.json'

Amplify.configure(amplifyconfig);

export default {
  setup () {
    return {
      drawer: ref(false),
      miniState: ref(true)
    }
  }
}
</script>

