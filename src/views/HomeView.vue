<script setup lang="ts">
  import TheWelcome from '../components/TheWelcome.vue'
  import HelloWorld from '../components/HelloWorld.vue'
  import 'quasar/src/css/index.sass'

  let message = "Expericoder 2.0"
</script>

<template>

    <div class="q-pa-md homeRow justify-center">

      <div class="row items-center">
        <div class="col-12 col-md-6"><HelloWorld :msg="message" /></div>
        <div class="col-12 col-md-6"><TheWelcome /></div>
      </div>
      
    </div>

</template>

<style lang="sass">
.homeRow
  max-width: 80%
  min-width: 1200px
  margin-left: auto
  margin-right: auto
  
.homeRow
  .row > div
    padding: 5px
    margin: 0px

  .row + .row
    margin-top: 1rem
.homeRow
    background: rgba(184, 195, 213, 0.15)
    border: 1px solid rgba(#999,.2)
</style>