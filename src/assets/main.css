@import './base.css';

#app {
  width: 100%;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: rgb(198, 48, 59);
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(0, 84%, 43%, 0.116);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    width: 100%;
    margin: 0 auto;
    padding: 2rem;
    font-weight: normal;
  }
}
