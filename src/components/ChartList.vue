<template>
  <div class="q-pa-md">
    <q-table
      flat bordered
      title="Charts"
      :rows="chartDataList"
      :columns="columns"
      row-key="fin"
    >

      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th auto-width />
          <q-th
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <!-- Quasar component for expanding row -->
            <q-btn size="sm" color="primary" square dense @click="props.expand = !props.expand" :icon="props.expand ? 'remove' : 'add'" />
          </q-td>
          <q-td
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.value }}
          </q-td>
        </q-tr>
        <q-tr v-show="props.expand" :props="props">
          <q-td colspan="100%">
            <div class="text-left">
                <q-card class="my-card" flat bordered>
                  <q-card-section horizontal>
                    <q-card-section class="q-pt-xs">
                      <div class="text-h5 q-mt-sm q-mb-xs">{{ props.row.name }}</div>
                      <div class="text-caption text-grey">
                        <div> MRN: {{ props.row.mrn }} </div>
                        <div> Date of Birth: {{ props.row.dob }} </div>
                        <div> Age: {{ props.row.age }} </div>
                        <div> Gender: {{ props.row.gender }} </div>
                      </div>
                    </q-card-section>
                  </q-card-section>

                  <q-separator />

                  <q-card-actions>
                    <q-btn size="md" square color="primary" label="Edit" :href="'/editor/' + $route.params.facility + '/' + props.row.encid" />
                  </q-card-actions>
                </q-card>
            </div>
          </q-td>
        </q-tr>
      </template>

    </q-table>
  </div>
</template>
  
<script lang="ts">
  import axios from "axios";
  import { QTable } from "quasar";
  import { fetchAuthSession } from 'aws-amplify/auth';

  const columns = [
      {name: 'fin', label: 'FIN', field: 'fin', sortable: true},
      {name: 'name', label: 'Name', field: 'name', sortable: true},
      {name: 'mrn', label: 'MRN', field: 'mrn', sortable: true},
      {name: 'dos', label: 'Date of Service', field: 'dos', sortable: true},
      {name: 'status', label: 'Status', field: 'status', sortable: true},
      {name: 'coder', label: 'Coder', field: 'coder', sortable: true}
    ]

  export default {
    name: "charts",

    data() {
      return {
        chartDataList: [{dos:'', fin:'', coder:'', status:'', name:'', mrn:'', dob:'', age:0}]
      };
    },

    methods: {
      async getChartData() {

        this.chartDataList.splice(0,1)
        const session = await fetchAuthSession();
        let config = {
        headers: {
          authorization: session.tokens?.idToken?.toString() || localStorage.getItem('jwt'),
          }
        }

        axios
          .get("https://vzw8xog2f7.execute-api.us-east-1.amazonaws.com/v2/encountersinit/byfac/" + this.$route.params.facility, config)
          .then(response => (this.restructureData(response.data)));
      },

      // Reconstruct chart data list from API call for viewing purposes
      restructureData(returnedData: [{dos:'', encid:'', ext_id:'', status:'', lastModifiedUser:'', demographics:{firstName:'', lastName:'', mrn:'', age:0, dob:'', gender:''}}]) {

        for (var counter = 0; counter < returnedData.length; counter++) {

            let chartDataRedux = {
                encid: returnedData[counter].encid,
                dos: returnedData[counter].dos,
                fin: returnedData[counter].ext_id,
                coder: returnedData[counter].lastModifiedUser,
                status: returnedData[counter].status,
                name: returnedData[counter].demographics.firstName + ' ' + returnedData[counter].demographics.lastName,
                mrn: returnedData[counter].demographics.mrn,
                dob: returnedData[counter].demographics.dob == '' ? 'Unknown' : returnedData[counter].demographics.dob,
                age: returnedData[counter].demographics.age == null ? 0: returnedData[counter].demographics.age,
                gender: returnedData[counter].demographics.gender == '' ? 'Unknown' : returnedData[counter].demographics.gender
              };
            
            this.chartDataList.push(chartDataRedux)
        }
      }
    },

    mounted: function () {
      this.getChartData()
    },

    setup() {

      return {
        columns
      }
    }
  };
</script>