<script>
  import { ref } from 'vue'
  import { useEncounterStore } from '@/stores/encounter'
  const tableTracker = ref(0);

  const procedureCodes = [
    {     
      value: '21315',
      label: '21315',
      description: 'Nasal fracture wo stabiliz/pc'
    },
    {
      value: '38300',
      label: '38300',
      description: 'Drng lymph node ab sim/pc'
    },
    {
      value: '40800',
      label: '40801',
      description: 'Drng abc mouth comp/pc'
    }
  ]

  const diagnosisCodes = [
    {
      value: ' J12.82',
      description: 'Pneumonia due to coronavirus disease 2019'
    },
    { 
      value: 'E55.9',
      description: 'Vitamin D deficiency, unspecified'
    },
    {
      value: 'R29.709',
      description: 'NIHSS score 9'
    },
    {
      value: 'S22.079S',
      description: 'Unspecified fracture of T9-T10 vertebra, sequela'
    },
    {
      value: 'S23.150S',
      description: 'Subluxation of T8/T9 thoracic vertebra, sequela'
    },
    {
      value: 'M26.211',
      description: "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>'s class I"
    },
    {
      value: 'M35.81',
      description: 'Multisystem inflammatory syndrome'
    },
    {
      value: 'J12.82',
      description: 'Pneumonia due to coronavirus disease 2019',
    },
    {
      value: 'A01.1',
      description: 'Paratyphoid fever A',
    },
    {
      value: 'A79.82',
      description: 'Anaplasmosis [A. phagocytophilum]'
    },
    {
      value: 'Z11.52',
      description: 'Encounter for screening for COVID-19'
    }
  ]

  const diagnosisCodesFiltered = ref(diagnosisCodes)
  const procedureCodesFiltered = ref(procedureCodes)
  
  const columns = [
    { name: 'selected', align: 'center', field: 'selected'},
    { name: 'code', align: 'left', label: 'Code', field: 'code' },
    { name: 'description', align: 'left', label: 'Description', field: 'description' },
    { name: 'priority', align: 'right', label: 'Priority', field: 'priority' },
    { name: 'cpt', align: 'right', label: 'Related Procedures', field: 'cpt' },
  ]

  export default {
    setup () {

      const encounter = useEncounterStore()
      const loading = ref(false)
      const rowsFiltered = ref([...encounter.diagnosisCodes])
      const recordRelocate = ref(-1)

      return {
        loading,
        columns,
        rowsFiltered,
        recordRelocate,
        tableTracker,
        diagnosisCodesFiltered,
        procedureCodesFiltered,

        onDragStart (e, priority) {
          console.log("Moving record " + priority + ".")
          recordRelocate.value = priority
        },

        reindex () {
          console.log("Reindexing.")
          let rowsReindexed = []
          for (let i = 0; i < encounter.diagnosisCodes.length; i++) {
            encounter.diagnosisCodes[i].priority = i
            rowsReindexed.push(encounter.diagnosisCodes[i])
          }
          rowsFiltered.value = rowsReindexed
        },

        onDrop (e, priority) {
          e.preventDefault()
          console.log("Dropping " + recordRelocate.value + " on record " + priority + ".")
          e.target.classList.remove('drag-enter')
          loading.value = true
          let rowsReordered = []
          let recordActual = encounter.diagnosisCodes[recordRelocate.value]
          if (recordRelocate.value > priority) {
            console.log("Shifting down.")
            encounter.diagnosisCodes.splice(priority, 0, recordActual);
            encounter.diagnosisCodes.splice(recordRelocate.value + 1, 1);
          } else if (recordRelocate.value < priority) {
            console.log("Shifting up.")
            encounter.diagnosisCodes.splice(priority, 0, recordActual);
            encounter.diagnosisCodes.splice(recordRelocate.value, 1);
          }
          for (let i = 0; i < encounter.diagnosisCodes.length; i++) {
            encounter.diagnosisCodes[i].priority = i
            rowsReordered.push(encounter.diagnosisCodes[i])
          }
          rowsFiltered.value = rowsReordered
          loading.value = false
        },

        onDragOver (e, priority) {
          e.preventDefault()
        },

        onDragEnter (e, priority) {
          console.log("Moving to record " + priority + ".")
          if (e.target.draggable !== true) {
            e.target.classList.add('drag-enter')
          }
        },

        onDragLeave (e, priority) {
          e.target.classList.remove('drag-enter')
        },

        addRow(){
          loading.value = true
          let indexNew = encounter.diagnosisCodes.length
          let recordBlank = {
                index: indexNew,
                priority: indexNew,
                selected: false,
                type: 'DX',
                code: '',
                description: '',
                cpt: [{ code: 'Encounter', category: "encounter"}]
              }
          rowsFiltered.value.push(encounter.diagnosisCodes[encounter.diagnosisCodes.push(recordBlank) - 1])
          loading.value = false
        },

        removeRow(){
          loading.value = true
          //Marked diagnosis codes will be removed. New array will be returned.
          rowsFiltered.value = encounter.removeDiagnosis();
          loading.value = false
        },

        //Filter diagnosis code list by code or description
        filterDiagnsosisCodes (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            let diagnsosisCodesRedux = []
            for (let i = 0; i < diagnsosisCodes.length; i++) {
              if (diagnsosisCodes[i].value.toLowerCase().indexOf(needle) > -1 || diagnsosisCodes[i].description.toLowerCase().indexOf(needle) > -1) {
                diagnsosisCodesRedux.push(diagnsosisCodes[i])
              }
            }
            diagnsosisCodesFiltered.value = diagnsosisCodesRedux
          })
        },

        //Filter procedure code list by code or description
        filterProcedureCodes (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            let procedureCodesRedux = []
            for (let i = 0; i < procedureCodes.length; i++) {
              if (procedureCodes[i].value.toLowerCase().indexOf(needle) > -1 || procedureCodes[i].description.toLowerCase().indexOf(needle) > -1) {
                procedureCodesRedux.push(procedureCodes[i])
              }
            }
            procedureCodesFiltered.value = procedureCodesRedux
          })
        },

        updateDescription (val) {
          loading.value = true

          let description = ''

          setTimeout(() => {
            for (let i = 0; i < diagnosisCodes.length; i++) {
              if (diagnosisCodes[i].value == val) {
                description = diagnosisCodes[i].description;
                break;
              }
            }

            for (let i = 0; i < encounter.diagnosisCodes.length; i++) {
              if (encounter.diagnosisCodes[i].code == val) {
                if (encounter.diagnosisCodes[i].description != description) {
                  encounter.diagnosisCodes[i].description = description;
                }
              }
            }
            loading.value = false
          }, 500)
        }

      }
    }
  }
</script>
<template>
    <q-table flat bordered
             title="DX" 
             :rows="rowsFiltered"
             :columns="columns"
             row-key="index"
             :key="tableTracker"
             :loading="loading"
             wrap-cells
             >
      <template v-slot:top>
        <q-btn color="primary" :disable="loading" label="Add row" @click="addRow" />
        <q-btn class="q-ml-sm" color="primary" :disable="loading" label="Remove Row" @click="removeRow" />
      </template>
      
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td colspan="100%" style="height: 5px; padding: 0px; border: none;" @dragenter="onDragEnter($event ,props.row.priority)" @dragleave="onDragLeave($event ,props.row.priority)" @dragover="onDragOver($event, props.row.priority)" @drop="onDrop($event, props.row.priority)">
          </q-td>
        </q-tr>
        <q-tr :props="props" draggable="true" @dragstart="onDragStart($event ,props.row.priority)">
          <q-td auto-width>
            <q-checkbox v-model="props.row.selected" />
          </q-td>
          
          <q-td key="code" :props="props">
            {{ props.row.code }}
            <q-popup-edit v-model="props.row.code" title="Select diagnosis code" buttons v-slot="scope">
              <q-select
                filled
                v-model="props.row.code"
                @update:model-value="updateDescription"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :options="diagnosisCodesFiltered"
                @filter="filterDiagnosisCodes"
                option-value="value"
                option-label="value"
                emit-value
                map-options

                hint="Type to filter"
                style="width: 250px; padding-bottom: 32px"
              >

                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section>
                      <q-item-label>{{ scope.opt.value }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </q-popup-edit>
          </q-td>

          <q-td key="description" :props="props">       
            <q-card flat bordered class="my-card" style="text-align: left">
              <q-card-section>
                {{ props.row.description }}
              </q-card-section>
            </q-card>
          </q-td>

          <q-td key="priority" :props="props">
            <q-chip :color="props.row.priority == 0 ? 'orange' : props.row.priority > 0 ? 'light-grey' : 'red'" square>
            {{ props.row.priority > 0 ? props.row.priority : 'Chief Complaint'}}
            </q-chip>       
          </q-td>

          <q-td key="cpt" :props="props">
            <q-chip v-show=false></q-chip>
            <template v-for="cpt in props.row.cpt">
              <q-chip
                :color="cpt.category == 'professional' ? 'red' : cpt.category == 'facility' ? 'blue' : 'grey'" 
                :icon="cpt.category == 'professional' ? 'assignment_ind' : cpt.category == 'facility' ? 'domain' : 'insert_chart'"
                square text-color="white">{{ cpt.code }}
              </q-chip>
            </template>
          </q-td>

        </q-tr>
      </template>
    </q-table>
</template>
<style scoped lang="sass">
.drag-enter
  outline-color: grey
  outline-style: solid
  outline-width: 1px
</style>