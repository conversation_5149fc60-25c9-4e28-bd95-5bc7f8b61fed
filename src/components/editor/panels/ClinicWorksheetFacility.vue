<script>
import { ref } from 'vue'
import { useQuasar } from 'quasar'

const selections = ref([])

const criticalCare = ref(0) //For storing the critical care time (minutes)
const commercialCare = ref(true)

const treeSelection = ref(null)
const treeKey = ref(0) //For forcing component update

const dismissals = []

const level = ref('No E&M')
const levelMap = [
    'No E&M',
    '99281',
    '99282',
    '99283',
    '99284',
    '99285',
    '99291',
    '99292'
]

const pseudoCodes = [
    'Q99281P3',
    'A79282M0',
    'K1992834',
    'M3928401',
    'L492N857',
    'N392091D',
    'PL39292E'
]

//Worksheet Options
const options= [
    [
        //No E&M
    ],
    [
        //99281
        { label: 'Initial Assessment', value: 'initial_assessment', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'No Medication or Treatments', value: 'medication_treatments', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Rx Refill Only, Asymptomatic', value: 'refill_only', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Note for Work or School', value: 'school_work_note', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Wound Recheck', value: 'wound_recheck', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Booster of Follow Up Immunization, No Acute Injury', value: 'booster', color: 'green', tooltiptext: 'Example could be series of rabies vaccines.', tooltipshow: true },
        { label: 'Dressing Changes (Uncomplicated)', value: 'dressing_changes', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Suture Removal (Uncomplicated)', value: 'suture_removal', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Discussion of Discharge Instructions (Straightforward)', value: 'discharge_instructions_straignthforward', color: 'green', tooltiptext: 'No medication given, no instructionsto use OTC medications, or home treatment required. Return if problems develop.', tooltipshow: true }
    ],
    [
        //99282
        { label: 'Tests by ED Staff (Urine Dip, Stool Hemoccult, Accucheck or Dextrostix)', value: 'low_risk_morbidity', color: 'lime', tooltiptext: 'Point of care testing performed at the beside.', tooltipshow: true},
        { label: 'Visual Acuity (Snellen)', value: 'visual_acuity', color: 'lime', tooltipshow: false, tooltiptext: '?' },
        { label: 'Obtain Clean Catch Urine', value: 'catch_urine', color: 'lime', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep or assist w/ Procedures Such as: Minor Laceration Repair, I&D of Simple Abscess, Crutch Training, FB Removal w/o Incision, etc.', value: 'occupational_therapy', color: 'lime', tooltipshow: false, tooltiptext: '?' },
        { label: 'Discussion of Discharge Instructions (Simple)', value: 'discharge_instructions_simple', color: 'lime', tooltiptext: 'No script medication given. Instructions to use OTC medicaitons as needed. Simple dressing, patient understands quickly and easily.', tooltipshow: true}
    ],
    [
        //99283
        { label: 'Receipt of EMS/Ambulance patient', value: 'moderate_risk_morbidity', color: 'amber', tooltiptext: 'Use this point for patient that arrive DOA. With no services provided by the ED outside of the post mortum care.', tooltipshow: true},
        { label: 'Heparin/Saline Lock', value: 'heparin_saline', color: 'amber', tooltiptext: 'Do not use this intervention for an IV that has anything ran besides a flush.', tooltipshow: true},
        { label: 'Nebulizer Treatment', value: 'nebuliser_treatment', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Preparation for Lab Tests Described in CPT (80048-87999 codes)', value: 'lab_test_preparation', color: 'amber', tooltiptext: 'Use for any lab test perfomed outside of the POCT test performed at the bedside.', tooltipshow: true },
        { label: 'Preparation for EKG', value: 'preparation_ekg', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Preparation for Plain X-rays of Only One Area (Hand, Shoulder, Pelvis, etc.)', value: 'preparation_xray', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prescription Medications Administered PO/Topical', value: 'prescription_medications', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'C-Spine Precautions', value: 'cspaine_precautions', color: 'amber', tooltiptext: 'This can be used for patients that arrive in C-Spine precauctions, or for patients that are placed in them after arrival to the ED.', tooltipshow: true },
        { label: 'Fluorescein Stain', value: 'fluorescein_stain', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Emesis/Incontinence Care', value: 'incontinence_care', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep or Assist w/ Procedures Such as: Joint Aspiration/Injection, Simple Fracture Care etc.', value: 'joint_aspiration', color: 'amber', tooltiptext: 'Enema, Complex I/D, Foreign Body removal with Incision, Simple Dislocation, Nail repair, Nerve block, Immunization Administration for injury, i.e., Dog bite, Prep for Breast/Genitalia Exam, Immunization Administration initial (with injury) ear irrigation.', tooltipshow: true },
        { label: 'Mental Health-Anxious, Simple Treatment', value: 'simple_treatment_mental', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Routine Psych Medical Clearance', value: 'psych_clearance', color: 'amber', tooltiptext: 'No work up preformed', tooltipshow: true},
        { label: 'Limited Social Worker Intervention', value: 'worker_intervention', color: 'amber', tooltiptext: 'Examples could be assistance with meal vouchers and/or transportion', tooltipshow: true },
        { label: 'Postmortem Care', value: 'post_mortem_care', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Direct Admit via ED', value: 'direct_admit', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Discussion of Discharge Instructions (Moderate Complexity)', value: 'discharge_instructions_moderate', color: 'amber', tooltiptext: 'Prescription ear drops, single prescription drug, Prescription for on OTC medicaiton (800 mg of Ibuprofen).  Include Head injury instructions, and weight bearing limitations.', tooltipshow: true}
    ],
    [
        //99284
        { label: 'Preparation for Two different Diagnostic Tests: (Labs, EKG, X-ray)', value: 'diagnostic_tests', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep for Plain X-ray (Multiple Body Areas): C-spine & Foot, Shoulder & Pelvis', value: 'xray_prep_plain', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep for Special Imaging Study (CT, MRI, Ultrasound, VQ Scans)', value: 'special_imaging_study', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Cardiac Monitoring', value: 'cardiac_monitoring', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Nebulizer Treatments', value: 'nebuliser_treatments', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Port-a-Cath Venous Access', value: 'pac_venous_access', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Administration and Monitoring of Infusions or Parenteral Medications (IV, IM, IO, SC)', value: 'admin_infusions', color: 'orange', tooltipshow: true, tooltiptext: 'Not for Immunization Administration.' },
        { label: 'NG/PEG Tube Placement/Replacement', value: 'tube_placement', color: 'orange', tooltipshow: true, tooltiptext: 'NG/PEG tube/Trachea/G-Tube/Complex catheter placement/replacement' },
        { label: 'Prep or Assist w/ Procedures Such as: Eye Irrigation with Morgan Lens, Bladder Irrigation With 3-way Foley, Pelvic Exam, Rectal Exam, Complex Laceration Repair, Facture w/ Manipulation etc.', value: 'procedure_prep_moderate', color: 'orange', tooltipshow: false },
        { label: 'Multiple Reassessments', value: 'multiple_reassessments', color: 'orange', tooltipshow: true, tooltiptext: 'Not comments that the patient is sleeping, resting, watching TV, etc. Must pertain to the visit' },
        { label: 'Sexual Assault Exam w/ Out Specimen Collection', value: 'sexual_assualt_exam_moderate', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Psychotic Patient; Not Suicidal', value: 'psychotic_patient', color: 'orange', tooltipshow: true, tooltiptext: 'Social Working or Behaviour Health Employee' },
        { label: 'Discussion of Discharge Instructions (Complex)', value: 'discharge_instructions_complex', color: 'orange', tooltipshow: true, tooltiptext: 'Two or more prescription medications. Do not use for OTC prespritions.' },
    ],
    [
        //99285
        { label: 'Requires Frequent Monitoring of Multiple Vital Signs (ie. 02 Sat, BP, Cardiac Rhythm, Respiratory Rate)', value: 'frequent_monitoring', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Preparation for ≥ 3 Diagnostic Tests: (Labs, EKG, X-ray)', value: 'prep_tests_three_or_more', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep for Special Imaging Study (CT, MRI, Ultrasound, VQ scan) Combined with Multiple Tests or Parenteral Medication or Oral or IV Contrast.', value: 'procedure_prep_special', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Administration of Blood Transfusion/Blood Products', value: 'blood_transfusion', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Moderate Sedation', value: 'moderate_sedation', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Oxygen via Face Mask or NRB', value: 'oxygen_mask', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Multiple Nebulizer Treatments: (Three) or More (If Nebulizer is Continuous, Each Twenty Minute Period is Considered Treatment)', value: 'nebuliser_treatments_multiple', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prep or Assist With Procedures Such as: Central Line Insertion, Gastric Lavage, LP, Paracentesis, Thoracentesis, Peritoneal Lavage, Arterial Intraosseous Line, etc.', value: 'procedure_prep_high', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Cooling or Heating Blanket', value: 'cooling_heating_blanket', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Extended Social Worker Intervention', value: 'extended_intervention', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Sexual Assault Exam w/ specimen collection by ED staff', value: 'sexual_assault_exam_high', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Coordination of Hospital Admission/Transfer or Change in Living Situation or Site', value: 'hospital_admission', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Physical/Chemical Restraints; Suicide Watch', value: 'physical_chemical_restraints', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Critical Care Less Than Thirty Minutes', value: 'critical_care_less_30', color: 'red', tooltipshow: false, tooltiptext: '?' }
    ],
    [
        //99291
        { label: '99291', value: 'critical_care', color: 'blue', tooltipshow: true, tooltiptext: 'Critical Care (Minimum)' },
    ],
    [
        //99292
        { label: '99292', value: 'critical_care_extended', color: 'blue', tooltipshow: true, tooltiptext: 'Critical Care (Beyond Maximum)' },
    ],
]

//Providers
const providers = [
    'Ernest Sorini MD', 
    'Harry Aretakis MD', 
    'John Bauer MD', 
    'Walter Kaniefski MD', 
    'Susan Secrete NP', 
    'Robilyn Collins NP', 
    'Jennifer Driggers PA',
    'Vincent Tuzze PA',
    'Bertsche Debra PA'
]

const providersFiltered = ref(providers)

const providerPrimary = ref(0)
const providerSecondary = ref(0)

const LevelOptions = ref(levelMap)

const modifierOptions = ref([
    '22', 
    '23', 
    '26', 
    '32', 
    '33', 
    '57', 
    '58', 
    '59', 
    '63'
])

const notes = ref('')

const records = ref([
    {
      active: true,
      index: 0,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    },
    {
      active: false,
      index: 1,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    },
    {
      active: false,
      index: 2,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    }
])

//Time based calculation guides
const criticalCareGuides = {
    government: {
        minimum: 30,
        maximum: 103,
        extended: 30
    },
    commercial: {
        minimum: 30,
        maximum: 74,
        extended: 30
    }
}

const summaryTree = [ { label: '' } ]

class node {
    constructor(label, key, children = []) {
        this.label = label
        this.children = children
        this.key = key
    }
}

export default {

    setup () {
        const $q = useQuasar()

        return {
            step: ref(1),

            treeSelection,
            treeSelect: ref(false),
            treeKey,

            pseudoCodeSelect: ref(false),

            criticalCare,
            commercialCare,

            options,
            selections,
            level,
            summaryTree,

            providersFiltered,

            providerPrimary,
            providerSecondary,

            LevelOptions,
            modifierOptions,
            pseudoCodes,

            notes,

            records,

            //Filter provider list
            filterProviders (val, update, abort) {
                update(() => {
                    const needle = val.toLowerCase()
                    providersFiltered.value = providers.filter(v => v.toLowerCase().indexOf(needle) > -1)
                })
            },

            treeDeselect () {
                //Clear summary tree selection
                treeSelection.value = null;
            },

            usePseudoCode (val) {
                console.log("Pseudo code selection.")
                if (val == true) {
                    LevelOptions.value = pseudoCodes
                } else if (val == false) {
                    LevelOptions.value = levelMap
                }
            },

            //Calculate Everything
            calculateLevel (val) {

                /*
                 ******************************
                 * Update Summary Tree when
                 * nodes are deselected
                 ******************************
                 */
                 if (treeSelection.value != null) {
                    if (selections.value.indexOf(treeSelection.value) > -1 && (treeSelection.value != 'critical_care' || treeSelection.value != 'critical_care_extended')) {
                        selections.value.splice(selections.value.indexOf(treeSelection.value), 1)
                        //Force tree update
                        treeKey.value += 1;
                    }
                    treeSelection.value = null;
                }

                /*
                 ******************************
                 * Critical Care Calculation
                 ******************************
                 */
                const criticalCareGuide = commercialCare.value ? criticalCareGuides.commercial : criticalCareGuides.government;

                if (criticalCare.value >= criticalCareGuide.minimum) {
                    //Select "critical care" by adding it to the selections array, and change label value in dataset
                    selections.value.push("critical_care")
                    options[6][0].label = '99291 x1'

                    if (criticalCare.value > criticalCareGuide.maximum) {
                        //Select "critical care extended" and change label to reflect "time multiplier"
                        //The changed label will be used for the summary tree as well
                        selections.value.push("critical_care_extended")
                        options[7][0].label = '99292 x' + Math.ceil((criticalCare.value - criticalCareGuide.maximum) / criticalCareGuide.extended)

                    } else {
                        //Deselect "critical care extended" by removing it from the selections array, and revert to initial label value in dataaset
                        //The changed label will be used for the summary tree as well
                        while (selections.value.indexOf("critical_care_extended") != -1) {
                            selections.value.splice(selections.value.indexOf("critical_care_extended"), 1)
                            options[7][0].label = '99292'
                        }
                    }
                } else {
                    //Deselect options and revert label changes
                    while (selections.value.indexOf("critical_care") != -1) {
                        selections.value.splice(selections.value.indexOf("critical_care"), 1)
                        options[6][0].label = '99291'
                    }

                    while (selections.value.indexOf("critical_care_extended") != -1) {
                        selections.value.splice(selections.value.indexOf("critical_care_extended"), 1)
                        options[7][0].label = '99292'
                    }
                }

                /*
                 ******************************
                 * E&M Level Calculation
                 ******************************
                 */
                let maximum = 0;
                let procedure = false;
                let categoriesNode = null; //Categories/level node for the summary tree

                //Clear summary tree
                while(summaryTree.length > 0) {
                    summaryTree.pop();
                }

                //Iterate through catgeories
                for (let categories = 0; categories < options.length; categories++) {
                    //Create nodes for each category/level except the last two. One node will be used for both catgeories.
                    //This code must be changed if the number of categories change (a "type" value would be better for tracking time-based procedures)
                    categoriesNode = categories < 7 ? new node(['Straightforward','Minimal','Low','Limited','Moderate','High','Critical Care'][categories], 'C' + categories) : categoriesNode;
                    //Iterate through individual options/procedures
                    for (let  item = 0; item < options[categories].length; item++) {
                        if (selections.value.indexOf(options[categories][item].value) > -1) {

                            categoriesNode.children.push(new node(options[categories][item].label, options[categories][item].value))
                            maximum = categories > maximum ? categories : maximum;

                            //Look for the selection of a non time based option
                            //Will be referenced later if a time based option was selected
                            //A user cannot select a time based option without also selecting a procedure
                            if (categories < 6) {
                                procedure = true;
                            }
                        }
                    }

                    //Capture loaded nodes (we'll use a single node for last two time based categories)
                    //This code must be changed if the number of categories change (a "type" value would be better for tracking time-based procedures)
                    if ((categoriesNode.children.length > 0 && categories < 6) || (categories > 6 && categoriesNode.children.length > 0)) {
                        summaryTree.push(categoriesNode)
                    }
                }

                /*
                 ******************************
                 * Update UI elements in final step
                 ******************************
                 */


                //Reset elements
                records.value.forEach(function(record) {
                    record.active = false
                    record.level = 'No E&M'
                    record.modifiers = []
                    record.notes = ''
                });

                //Populate elements
                if (maximum < 7) {

                    records.value[0].level = levelMap[maximum]
                    records.value[0].active = true
                    records.value[0].multiplier = 1

                    level.value = levelMap[maximum]

                } else if (maximum == 7) {

                    records.value[0].active = true
                    records.value[0].level = '99291'
                    records.value[0].multiplier = 1

                    records.value[1].active = true
                    records.value[1].level = '99292'
                    records.value[1].multiplier = Math.ceil((criticalCare.value - criticalCareGuide.maximum) / criticalCareGuide.extended)

                    level.value = '99291 & 99292'
                }

                //Display alert if a time based selection is made but no procedure is selected
                if (procedure == false && maximum > 5) {
                    dismissals.push($q.notify({
                        group: true,
                        html: true,
                        type: 'negative',
                        position: 'bottom',
                        message: 'A <i>time based</i> option has been selected but <u>no</u> procedure has been selected.',
                        caption: 'This error must be corrected.',
                        color: 'deep-orange-9',
                        badgeColor: 'blue-grey-7',
                        timeout: 0,
                        //icon: 'announcement',
                        actions: [
                            { icon: 'close', color: 'white', round: true, handler: () => { } }
                        ]
                    }))
                } else {
                    for (let iterate = 0; iterate < dismissals.length; iterate++) {
                        dismissals[iterate]()
                    }
                }
            }
        }
    }
}
</script>
<template>
    <q-stepper
        v-model="step"
        vertical
        color="primary"
        animated
        header-nav
        flat
    >

        <q-step
            :name="1"
            title="Procedures"
            caption="Procedures Categorised by Level"
            icon="description"
            :done="step > 1"
        >

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-green">
                    Minimal
                </q-banner>

                <br>

                <q-option-group
                    :options="options[1]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">   
                <q-banner inline-actions class="text-white bg-lime">
                    Low
                </q-banner>

                <br>

                <q-option-group
                    :options="options[2]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>`
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-amber">
                    Limited
                </q-banner>

                <br>

                <q-option-group
                    :options="options[3]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-orange">
                    Moderate
                </q-banner>

                <br>

                <q-option-group
                    :options="options[4]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-red">
                    High
                </q-banner>

                <br>

                <q-option-group
                    :options="options[5]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-blue">
                    Critical Care
                </q-banner>

                <br>

                <q-input
                    v-model="criticalCare"
                    type="number"
                    style="max-width: 300px"
                    label = "Critical care time (minutes)"
                    filled
                    @update:model-value="calculateLevel"
                    :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                />

                <q-checkbox v-model="commercialCare" @update:model-value="calculateLevel" label="Commercial Care" />

                <q-option-group
                    :options="options[6]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>

                <q-option-group
                    :options="options[7]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <q-stepper-navigation>
                <q-btn @click="step = 2" color="primary" label="Continue" />
            </q-stepper-navigation>

        </q-step>


        <!-- Panel which shows summary of everything selected in previous steps -->
        <q-step
            :name="2"
            title="Selection Summary"
            icon="playlist_add_check"
            caption="Overview of Selections and Calculated Level"
            :done="step > 2"
        >
            <!-- Summary tree nodes are created and linked during in level calculation function -->
            <q-tree
                :nodes="summaryTree"
                node-key="key"
                default-expand-all
                v-model:selected="treeSelection"
                @update:selected="treeSelect = true"
                :key="treeKey"
            />

            <q-chip square>
                <q-avatar icon="equalizer" color="red" text-color="white" />
                {{ level }}
            </q-chip>

            <q-stepper-navigation>
                <q-btn @click="step = 3" color="primary" label="Finish" />
                <q-btn flat @click="step = 1" color="primary" label="Back" class="q-ml-sm" />
            </q-stepper-navigation>
        </q-step>

        <!-- Final panel which shows the data that will be submitted -->
        <q-step
            :name="3"
            title="Final"
            icon="description"
            caption="Codes, Modifiers, Providers and Notes."
        >

        <div class="q-pa-md row items-start q-gutter-md">
            <q-card class="my-card" flat bordered style="width: 280px; min-height: 460px;" v-if="records[0].active">
                <q-card-section style="min-height: 75px;">
                    <div class="row items-center no-wrap">
                        <div class="col">
                            <q-item-section>
                                <q-item-label>Level</q-item-label>
                                <q-item-label caption>Level and Modifiers</q-item-label>
                            </q-item-section>
                        </div>
                    
                        <div class="col-auto">
                            <q-btn color="grey-7" round flat icon="edit_note">
                            <q-menu cover auto-close>
                                <q-list>
                                <q-item clickable @click="pseudoCodeSelect = true">
                                    <q-item-section>Pseudo Code</q-item-section>
                                </q-item>
                                </q-list>
                            </q-menu>
                            </q-btn>
                        </div>
                    </div>
                </q-card-section>

                <q-separator />

                <q-card-section>
                    <q-select
                        filled
                        v-model="records[0].level"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        :options="LevelOptions"
                        hint="E&M Level"
                        >
                        <template v-slot:no-option>
                            <q-item>
                            <q-item-section class="text-grey">
                                No results
                            </q-item-section>
                            </q-item>
                        </template>
                    </q-select>
                    <q-input
                        v-model="records[0].multiplier"
                        type="number"
                        style="max-width: 80px"
                        hint="Multiplier"
                        filled
                        :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                    />
                </q-card-section>

                <q-card-section class="bg-grey-2" style="margin-left: 14px; margin-right: 14px; min-height: 95px; border-radius: 4px;">
                    <q-chip v-show=false></q-chip>
                    <q-chip color="red" square text-color="white" v-for="mod in records[0].modifiers">{{ mod }}</q-chip>
                    <q-popup-edit v-model="records[0].modifiers" v-slot="scope">
                        <q-select
                            v-model="records[0].modifiers"
                            multiple
                            :options="modifierOptions"
                            use-chips
                            stack-label
                            label="Select or remove modifiers"
                        />
                    </q-popup-edit>
                </q-card-section>
                <q-item-section style="margin-left: 24px; margin-top: 8px;">
                    <q-item-label caption>Modifiers</q-item-label>
                </q-item-section>
            </q-card>


            <q-card class="my-card" flat bordered style="width: 280px; min-height: 460px;">

                <q-card-section style="min-height: 75px;">
                    <div class="row items-center no-wrap">
                        <q-item-section>
                            <q-item-label>Providers</q-item-label>
                            <q-item-label caption>Primary and Secondary Providers</q-item-label>
                        </q-item-section>
                    </div>
                </q-card-section>

                <q-separator />

                <q-card-section>
                    <q-select
                        filled
                        v-model="providerPrimary"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        :options="providersFiltered"
                        @filter="filterProviders"
                        hint="Type to filter primary providers"
                        style="width: 250px; padding-bottom: 32px"
                        >
                        <template v-slot:no-option>
                            <q-item>
                            <q-item-section class="text-grey">
                                No results
                            </q-item-section>
                            </q-item>
                        </template>
                    </q-select>

                    <br/>

                    <q-select
                        filled
                        v-model="providerSecondary"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        :options="providersFiltered"
                        @filter="filterProviders"
                        hint="Type to filter secondary providers"
                        style="width: 250px; padding-bottom: 32px"
                        >
                        <template v-slot:no-option>
                            <q-item>
                            <q-item-section class="text-grey">
                                No results
                            </q-item-section>
                            </q-item>
                        </template>
                    </q-select>
                </q-card-section>
            </q-card>

            <q-card class="my-card" flat bordered style="width: 280px; min-height: 460px;">
                <q-card-section style="min-height: 75px;">
                    <div class="row items-center no-wrap">
                        <q-item-section>
                            <q-item-label>Notes</q-item-label>
                            <q-item-label caption>Coding Notes</q-item-label>
                        </q-item-section>
                    </div>
                </q-card-section>

                <q-separator />

                <q-card-section>
                    <q-input
                        v-model="notes"
                        filled
                        type="textarea"
                        autogrow
                    />
                </q-card-section>
            </q-card>

            <q-card class="my-card" flat bordered style="width: 280px; min-height: 460px;" v-if="records[1].active">
                <q-card-section style="min-height: 75px;">
                    <div class="row items-center no-wrap">
                        <div class="col">
                        <q-item-section>
                            <q-item-label>Level</q-item-label>
                            <q-item-label caption>Level and Modifiers</q-item-label>
                        </q-item-section>
                    </div>
                    
                </div>
                </q-card-section>

                <q-separator />

                <q-card-section>
                    <q-select
                        filled
                        v-model="records[1].level"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        :options="LevelOptions"
                        hint="E&M Level"
                        >
                        <template v-slot:no-option>
                            <q-item>
                            <q-item-section class="text-grey">
                                No results
                            </q-item-section>
                            </q-item>
                        </template>
                    </q-select>
                    <q-input
                        v-model="records[1].multiplier"
                        type="number"
                        style="max-width: 80px"
                        hint="Multiplier"
                        filled
                        :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                    />
                </q-card-section>
            </q-card>

        </div>  

        </q-step>
    
    </q-stepper>

    <!-- Node removal warning dialogue. -->
    <q-dialog v-model="treeSelect" seamless transition-show="flip-down" transition-hide="flip-up">
        <q-card class="bg-red text-white shadow-24" style="width: 300px">
            <q-card-section>
                <div class="text-h6">Warning!</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
                Are you sure you want to remove this selection?
            </q-card-section>

            <q-card-actions align="right" class="bg-white text-primary">
                <q-btn flat label="No" color="primary" v-close-popup @click="treeDeselect" />
                <q-btn flat label="Yes" color="primary" v-close-popup @click="calculateLevel" />
            </q-card-actions>
        </q-card>
    </q-dialog>

    <!-- Pseudo code warning dialogue. -->
    <q-dialog v-model="pseudoCodeSelect" seamless transition-show="flip-down" transition-hide="flip-up">
        <q-card class="bg-red text-white shadow-24" style="width: 300px">
            <q-card-section>
                <div class="text-h6">Warning!</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
                Do you want to use a pseudo code?
            </q-card-section>

            <q-card-actions align="right" class="bg-white text-primary">
                <q-btn flat label="No" color="primary" v-close-popup @click="usePseudoCode(false)" />
                <q-btn flat label="Yes" color="primary" v-close-popup @click="usePseudoCode(true)" />
            </q-card-actions>
        </q-card>
    </q-dialog>
    
</template>