<script>
import { ref } from 'vue'

const selections = ref([])

const categoryOne = { "unique_source_review": 0, "unique_test_review": 0, "unique_test_ordering": 0 };
const categoryOneBind = ref(categoryOne)

const treeSelection = ref(null)
const treeKey = ref(0) //For forcing component update

const criticalCare = ref(0)
const commercialCare = ref(true)

const level = ref('No E&M')
const levelMap = [
    'No E&M',
    '99281',
    '99282',
    '99283',
    '99284',
    '99285',
    '99291',
    '99292'
]

const pseudoCodes = [
    'Q99281P3',
    'A79282M0',
    'K1992834',
    'M3928401',
    'L492N857',
    'N392091D',
    'PL39292E'
]

//Chart A Options
const optionsChartA = [
    [
        //Straightforward
    ],
    [
        //Limited
    ],
    [
        //Minimial (99282)
        { label: 'Self-Limited Problem', value: 'minor_problem_minimal', color: 'green', tooltipshow: true, tooltiptext: 'A problem that runs a definite and prescribed course, is temporary in nature, and is not likely to permanetently affect the patient`s health status.' }
    ],
    [
        //Low (99283)
        { label: '2+ Self-Limited/Minor Problem', value: 'minor_prob', color: 'amber', tooltipshow: true, tooltiptext: "A problem that runs a definite and prescribed course, is temporary in nature, and is not likely to permanently affect the patient's health status." },
        { label: '1 Stable Chronic Illness', value: 'chronic_ill', color: 'amber', tooltipshow: true, tooltiptext: 'A problem with an expected duration of at least one year or until the death of the patient. For the purpose of defining chronicity, conditions are treated as chronic whether or not the stage or the severity changes (e.g., uncontrolled diabetes and controlled diabetes are a single chronic condition). "Stable" for the purposes of calculating medical decision making is defined by the specific treatment goal(s) for an individual patient. A patient that is not at their treatment goal is not stable, even if the condition has not changed and there is no short- term threat to life or bodily function. For example, a patient with persistently poorly controlled blood pressure for whom better control is a goal is not stable, even if the pressures are not changing and the patient is asymptomatic. The risk of morbidity without treatment is significant. Examples may include well-controlled hypertension, non-insulin dependent diabetes, cataract, or benign prostatic hyperplasia. The risk of morbidity without treatment is significant.' },
        { label: '1 Stable Acute Illness', value: 'acute_ill', color: 'amber' ,tooltipshow: true, tooltiptext: 'A problem that is new or recent for which treatment has been initiated. The patient is improved and, while resolution may not be complete, is stable with respect to this condition.' },
        { label: '1 Acute Uncomplicated Illness/Injury', value: 'uncomplicated_ill', color: 'amber', tooltipshow: true, tooltiptext: 'A recent or new short-term problem with low risk of morbidity for which a treatment is considered. There is little to no risk of mortality with treatment is considered.  There is little to no risk of mortality with treatment and full recovery without functional impairment is expected. A problem that is normally self-limited or minor, but is not resolving consistent with a definite and prescribed course is an acute uncomplicated illness. Examples may include cystitis, allergic rhinitis, or a simple sprain.' },
        { label: '1 Acute Uncomplicated Illness/Injury requiring hospital inpatient or observation care', value: 'uncomplicated_ill_hospital', color: 'amber', tooltipshow: true, tooltiptext: 'A recent or new short-term problem with low risk of morbidity for which treatment is required.  There is little to no risk of mortality with treatment, and full recovery without functional impairment is expected.  The treatment required is delivered in a hospital inpatient or observation level setting.' }
    ],
    [
        //Moderate (99284)
        { label: '1+ Chronic Illness w/ exacerbation, progression or Tx side effects', value: 'chron_ill_progression', color: 'orange', tooltipshow: true, tooltiptext: 'A chronic illness that is acutely worsening, poorly controlled, uncontrolled, or progressing with an intent of controlling progression and requiring additional supportive care or requiring attention to treatment for side effects, but does not require consideration of hospital level of care.' },
        { label: '2+ Stable Chronic Illness', value: 'chronic_ill_mod', color: 'orange', tooltipshow: true, tooltiptext: "A problem that runs a definite and prescribed course, is temporary in nature, and is not likely to permanently affect the patient's health status." },
        { label: '1 Undiagnosed problem w/ uncertain prognosis', value: 'undiag_uncert', color: 'orange', tooltipshow: true, tooltiptext: 'A problem in the differential diagnosis that represents a condition likely to result in a high risk of morbidity without medical intervention. An example is a lump in the breast.' },
        { label: '1 Acute Illness w/ Systemic Symptoms', value: 'acute_ill_sys', color: 'orange', tooltipshow: true, tooltiptext: "An illness that causes systemic symptoms (symptoms affecting one or more organ systems) and has a high risk of morbidity without medical intervention treatment. For systemic general symptoms such as fever, body aches or fatigue in a minor illness that may be treated to alleviate symptoms, shorten the course of illness or  to prevent complications, see the definitions for 'self-limited or minor' or acute, uncomplicated illness or injury.' Systemic symptoms may not be general, but may be single system. Examples may include pyelonephritis, pneumonitis, or colitis." },
        { label: '1 Acute Complicated Injury', value: 'acute_complicated_injury', color: 'orange', tooltipshow: true, tooltiptext: "An injury which requires treatment medical intervention that includes evaluation of other body systems that are not directly related to the injured organ, the injury is extensive, or the treatment options are multiple and/or associated with risk of morbidity. An example is a head injury with brief loss of consciousness, multiple fractures, multiple injuries, etc." }
    ],
    [
        //High (99285)
        { label: '1+ Chronic Illness w/ Severe exacerbation, progression or Tx side effects', value: 'chronic_ill_severe', color: 'red', tooltipshow: true, tooltiptext: 'The severe exacerbation or progression of a chronic illness or severe side effects of treatment that have significant risk of morbidity and may require hospitalization escalation in level of care.' },
        { label: '2+ Acute/Chronic Illness/Injury that pose threat to life or bodily function', value: 'acute_chronic_ill', color: 'red', tooltipshow: true, tooltiptext: 'An acute illness with systemic symptoms (symptoms affecting one or more organ systems), or an acute complicated injury, or a chronic illness or injury with exacerbation and/or progression or side effects of treatment, that poses a threat to life or bodily function in the near short-term without treatment. Some symptoms may represent a condition that is significantly probable and poses a potential threat to life or bodily function.  These may be included in this category when the evaluation and treatment are consistent with this degree of potential severity.  Examples may include acute myocardial infarction, pulmonary embolus, severe respiratory distress, progressive severe rheumatoid arthritis, psychiatric illness with potential threat to self or others, peritonitis, acute renal failure, or an abrupt change in neurologic status.' }
    ]
]

//Chart B Options
const optionsChartB = [
    [
        //Category One
        { label: 'Review of prior external note(s) from each unique source', value: 'unique_source_review', color: 'blue', tooltipshow: false, tooltiptext: '?' },
        { label: 'Review of the result(s) of each unique test', value: "unique_test_review", color: 'blue', tooltipshow: false, tooltiptext: '?' },
        { label: 'Ordering of each unique test', value: "unique_test_ordering", color: 'blue', tooltipshow: false, tooltiptext: '?' }
    ],
    [
        //Category Two
        { label: 'Independant historian(s) (eg, parent, guardian, surrogate, spouses, witness, EMS)', value: 'independent_historian', color: 'blue', tooltipshow: true, tooltiptext: 'An individual such as a parent, guardian, surrogate, spouse, care giver, witness, who provides a history in addition to a history provided by the patient who is unable to provide a complete or reliable history due to developmental stage of the patient, dementia, or psychosis or another mental condition(s) or because a confirmatory history is determined to be necessary or because a confirmatory history is judged to be necessary. In the case where there may be conflict or poor communication between multiple historians and more than one historian(s) is needed, the independent historian(s) requirement is met.  It does not include translation services. The independent history does not need to be obtained in person but does need to be obtained directly from the historian providing the independent information.' },
        { label: 'Independent interpretation of a test performed by another physician/other qualified health care professionals (not separately reported)', value: 'independent_interpretation', color: 'blue', tooltipshow: true, tooltiptext: 'The interpretation of a test for which there is a CPT® code and an interpretation or report is expected. This does not apply when the provider is reporting the service or has previously reported the service for the patient. A form of interpretation should be documented, but need not conform to the usual standards of a complete report for the test.' }
    ],
    [
        //Category Three
        { label: 'Discussion of management or test interpretion with external physician/other qualified health care professional/appropriate source (not separately reported)', value: 'discussion', color: 'blue', tooltipshow: true, tooltiptext: 'Discussion requires an interactive exchange. The exchange must be direct and not through intermediaries (eg, clinical staff or trainees).  Sending chart notes or written exchanges that are within progress notes does not qualify as an interactive exchange.   Does not need to be on the date of the encounter, in person, but must be initiated and completed within a short time period (eg, within a day or two).  -- External - External records, communications and/or test results are from an external provider, other qualified health care processional,  facility or healthcare organization. -- External Physician or Other Qualified Healthcare Professional - An external physician or other qualified health care professional is an individual who is in a different group practice or who is of a different specialty or subspecialty. It includes licensed professionals that are practicing independently. It may also be a facility or organizational provider such as a hospital, nursing facility, or home health care agency. -- Appropriate Source - For the purpose of the Discussion of Management Data Element, an appropriate source includes individuals who are not health care professionals, but may be involved in the management of the patient (e.g., lawyer, parole officer, power of attorney, case manager, clergy, teacher). It does not include discussion with family or informal caregivers.' }
    ]
]      

/**
 * Below is the slection matrix for "Chart B" level calculation
 * created from documentation (refer to Table B Excel document).
 * Each array element can be mapped to document detailing E&M level 
 * determination for Chart B. The first element of the children arrays 
 * indicates the Excel sheet column while the last element indicates 
 * the level produced by the combination. The combination does not
 * represent an exact match, it represents the minimum needed to meet
 * that level. The elements between 0 and 7 store the 
 * following (number represents index):
 * 
 *      1. Review of prior external note(s) from each unique source 
 *         (an integer 0 or greater)
 * 
 *      2. Review of the result(s) of each unique test (an integer 
 *         zero or greater)
 * 
 *      3. Ordering of each unique test (an integeter zero or greater)
 * 
 *      4. An individual (eg, parent, guardian, surrogate, sposues, 
 *         witness) who provides a history in additional to patient 
 *         (a boolean in the form of an integer of 0 or 1)
 * 
 *      5. Independent interpretation of a test performed by another 
 *         physician/other qualified health care professionals (not 
 *         separately reported) (a boolean in the form of an integer 
 *         of 0 or 1)
 * 
 *      6. Discussion of management or testing interpreation with 
 *         externalphysician/orther qualified health care professional/appropriate 
 *         source (not separately reported) (a boolean in the form of an 
 *         integer of 0 or 1)
 * 
 * A drastic enough change in documentation/business rules may 
 * necessitate a change in this matrix and a change in the logic which 
 * requires the matrix.
 */
const selectionMatrixChartB = [
    //Combinations for a "none" result
    [ 'B',0,0,0,0,0,0,0],

    //Combinations for a "low" result
    [ 'C',1,1,0,0,0,0,2],
    [ 'D',0,1,1,0,0,0,2],
    [ 'E',1,0,1,0,0,0,2],
    [ 'F',0,0,0,1,0,0,2],
    [ 'G',2,0,0,0,0,0,2],
    [ 'H',0,2,0,0,0,0,2],
    [ 'I',0,0,2,0,0,0,2],

    //Combinations for a "moderate" result
    [ 'J',1,1,1,0,0,0,4],
    [ 'K',2,1,0,0,0,0,4],
    [ 'L',0,2,1,0,0,0,4],
    [ 'M',0,1,2,0,0,0,4],
    [ 'N',1,0,2,0,0,0,4],
    [ 'O',1,2,0,0,0,0,4],
    [ 'P',1,1,0,1,0,0,4],
    [ 'Q',0,1,1,1,0,0,4],
    [ 'R',2,0,0,1,0,0,4],
    [ 'S',0,2,0,1,0,0,4],
    [ 'T',0,0,2,1,0,0,4],
    [ 'U',1,0,1,1,0,0,4],
    [ 'V',0,0,0,0,1,0,4],
    [ 'W',0,0,0,0,0,1,4],

    //Combinations for a "high" result
    [ 'X',1,1,1,0,1,0,5],
    [ 'Y',1,1,1,0,0,1,5],
    [ 'Z',1,1,0,1,1,0,5],
    ['AA',1,1,0,1,0,1,5],
    ['AB',1,0,1,1,1,0,5],
    ['AC',1,0,1,1,0,1,5],
    ['AD',0,1,1,1,1,0,5],
    ['AE',0,1,1,1,0,1,5],
    ['AF',2,1,0,0,1,0,5],
    ['AG',2,1,0,0,0,1,5],
    ['AH',0,2,1,0,1,0,5],
    ['AI',0,2,1,0,0,1,5],
    ['AJ',0,1,2,0,1,0,5],
    ['AK',0,1,2,0,0,1,5],
    ['AL',0,0,0,0,1,1,5],
    ['AM',3,0,0,0,1,0,5],
    ['AN',3,0,0,0,0,1,5],
    ['AO',0,3,0,0,1,0,5],
    ['AP',0,3,0,0,0,1,5],
    ['AQ',0,0,3,0,1,0,5],
    ['AR',0,0,3,0,0,1,5]
]

//Chart C Options
const optionsChartC = [               
    [
        //Straightforward
    ],
    [
        //Limited?
    ],
    [
        //Minimal (99282)
        { label: 'Minimal risk of morbidity from additional diagnostic testing or treatment', value: 'minimal_risk', color: 'green', tooltipshow: true, tooltiptext: "Risk - The probability and/or consequences of an event (an event is the medical intervention or treatment). The assessment of the level of risk is affected by the nature of the medical intervention or treatment under consideration. For example, a low probability of death may be high risk, whereas a high chance of a minor, self-limited adverse effect of treatment may be low risk. Definitions of risk are based upon the usual behavior and thought processes of a provider in the same specialty. Trained clinicians apply common language usage meanings to terms such as 'high', 'medium', 'low', or 'minimal' risk and do not require quantification for these definitions, (though quantification may be provided when evidence-based medicine has established probabilities). For the purposes of calculating medical decision making, level of risk is based upon consequences of the problem(s) addressed at the visit when appropriately treated. Risk also includes medical decision making related to the need to initiate or forego further testing, treatment and/or hospitalization. -- Morbidity - A state of illness or functional impairment that is expected to be long-term duration in which function is limited, quality of life is impaired, or there is organ damage that may not be temporary despite treatment." },
        { label: 'Rest', value: 'rest', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Gargles', value: 'gargles', color: 'green' },
        { label: 'Elastic Bandages', value: 'elastic_bandages', color: 'green', tooltipshow: false, tooltiptext: '?' },
        { label: 'Superficial Dressing', value: 'superficial_dressing', color: 'green', tooltipshow: false, tooltiptext: '?' }
    ],
    [
        //Low (99283)
        { label: 'Low risk of morbidity from additional diagnostic testing or Treatment', value: 'low_risk_morbidity', color: 'amber', tooltipshow: true, tooltiptext: "Risk - The probability and/or consequences of an event (an event is the medical intervention or treatment). The assessment of the level of risk is affected by the nature of the medical intervention or treatment under consideration. For example, a low probability of death may be high risk, whereas a high chance of a minor, self-limited adverse effect of treatment may be low risk. Definitions of risk are based upon the usual behavior and thought processes of a provider in the same specialty. Trained clinicians apply common language usage meanings to terms such as 'high', 'medium', 'low', or 'minimal' risk and do not require quantification for these definitions, (though quantification may be provided when evidence-based medicine has established probabilities). For the purposes of calculating medical decision making, level of risk is based upon consequences of the problem(s) addressed at the visit when appropriately treated. Risk also includes medical decision making related to the need to initiate or forego further testing, treatment and/or hospitalization. -- Morbidity - A state of illness or functional impairment that is expected to be long-term duration in which function is limited, quality of life is impaired, or there is organ damage that may not be temporary despite treatment." },
        { label: 'Minor surgery with no identified risk factors', value: 'minor_surgery', color: 'amber', tooltipshow: true, tooltiptext: "Surgery - Minor or Major: The classification of surgery into minor or major is based on the common meaning of such terms when used by trained clinicians, similar to the use of the term 'risk.' These terms are not defined by a surgical package classification. -- Surgery - Elective or Emergency:  Elective procedures and emergent or urgent procedures describe the timing of a procedure is typically planned in advanced (eg, scheduled for weeks later), while an emergent procedure is typically performed immediately or with minimal delay to allow for patient stabilization.  Both elective and emergent procedures may be minor or major procedures. -- Surgery - Risk Factors, Patient or Procedure:  Risk factors are those that are relevant to the patient and procedure. Evidence based risk calculators may be used, but are not required, in assessing patient and procedure risk." },
        { label: 'Physical therapy', value: 'physical_therapy', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'Occupational therapy', value: 'occupational_therapy', color: 'amber', tooltipshow: false, tooltiptext: '?' },
        { label: 'IV fluids without additives', value: 'iv_fluids', color: 'amber', tooltipshow: false, tooltiptext: '?' }
    ],
    [
        //Moderate (99284)
        { label: 'Moderate risk morbidity from additional diagnostic testing or treatment', value: 'moderate_risk_morbidity', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Prescription drug management', value: 'prescription_drug_management', color: 'orange', tooltipshow: false, tooltiptext: '?' },
        { label: 'Decision regarding minor surgery with identified patient or procedure risk factors', value: 'decision_surgery_minor', color: 'orange', tooltipshow: true, tooltiptext: "Surgery - Minor or Major: The classification of surgery into minor or major is based on the common meaning of such terms when used by trained clinicians, similar to the use of the term 'risk.' These terms are not defined by a surgical package classification. -- Surgery - Elective or Emergency:  Elective procedures and emergent or urgent procedures describe the timing of a procedure is typically planned in advanced (eg, scheduled for weeks later), while an emergent procedure is typically performed immediately or with minimal delay to allow for patient stabilization.  Both elective and emergent procedures may be minor or major procedures. -- Surgery - Risk Factors, Patient or Procedure:  Risk factors are those that are relevant to the patient and procedure.  Evidence based risk calculators may be used, but are not required, in assessing patient and procedure risk." },
        { label: 'Decision regarding elective major surgery without identified patient or protective procedure risk factors', value: 'decision_surgery_elective', color: 'orange', tooltipshow: true, tooltiptext: "Surgery - Minor or Major: The classification of surgery into minor or major is based on the common meaning of such terms when used by trained clinicians, similar to the use of the term 'risk.' These terms are not defined by a surgical package classification. -- Surgery - Elective or Emergency:  Elective procedures and emergent or urgent procedures describe the timing of a procedure is typically planned in advanced (eg, scheduled for weeks later), while an emergent procedure is typically performed immediately or with minimal delay to allow for patient stabilization.  Both elective and emergent procedures may be minor or major procedures. -- Surgery - Risk Factors, Patient or Procedure:  Risk factors are those that are relevant to the patient and procedure.  Evidence based risk calculators may be used, but are not required, in assessing patient and procedure risk." },
        { label: 'Diagnosis or treatment significantly limited by social determinants of health', value: 'diagnosis_treatment', color: 'orange', tooltipshow: false, tooltiptext: '?' }
    ],
    [
        //High (99285)
        { label: 'High risk of morbidity from additional diagnostic testing or treatment', value: 'high_risk_morbidity', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Drug therapy requiring intensive monitoring for toxicity', value: 'drug_therapy', color: 'red', tooltipshow: true, tooltiptext: "A drug that requires intensive monitoring is a therapeutic agent which has the potential to cause serious morbidity or death. Monitoring is performed for assessment of potential adverse effects, not primarily for assessment of the therapeutic effect. Monitoring should follow practice that is generally accepted for the drug, but may be patient specific in some cases. Intensive monitoring may be long-term or short term. Long-term intensive monitoring is performed not less than quarterly. Monitoring may include a lab test, a physiologic test or imaging. Monitoring by history or examination does not qualify. The monitoring affects the level of medical decision making in a visit in which it is considered in the management of the patient. Examples may include monitoring for a cytopenia in the use of an antineoplastic agent between dose cycles or the short-term intensive monitoring of electrolytes and renal function in a patient who is undergoing diuresis. Examples of monitoring that does not qualify include monitoring glucose levels during insulin therapy as the primary reason is the therapeutic effect (even if hypoglycemia is a concern); or annual electrolytes and renal function for a patient on a diuretic as the frequency does not meet the threshold." },
        { label: 'Decision regarding elective major surgery with identified patient or procedure risk factor', value: 'decision_surgery_elective_risk', color: 'red', tooltipshow: true, tooltiptext: "Surgery - Minor or Major: The classification of surgery into minor or major is based on the common meaning of such terms when used by trained clinicians, similar to the use of the term 'risk.' These terms are not defined by a surgical package classification. -- Surgery - Elective or Emergency:  Elective procedures and emergent or urgent procedures describe the timing of a procedure is typically planned in advanced (eg, scheduled for weeks later), while an emergent procedure is typically performed immediately or with minimal delay to allow for patient stabilization.  Both elective and emergent procedures may be minor or major procedures. -- Surgery - Risk Factors, Patient or Procedure:  Risk factors are those that are relevant to the patient and procedure.  Evidence based risk calculators may be used, but are not required, in assessing patient and procedure risk." },
        { label: 'Decision regarding emergency major surgery', value: 'decision_surgery_major', color: 'red', tooltipshow: true, tooltiptext: "Surgery - Minor or Major: The classification of surgery into minor or major is based on the common meaning of such terms when used by trained clinicians, similar to the use of the term 'risk.' These terms are not defined by a surgical package classification. -- Surgery - Elective or Emergency:  Elective procedures and emergent or urgent procedures describe the timing of a procedure is typically planned in advanced (eg, scheduled for weeks later), while an emergent procedure is typically performed immediately or with minimal delay to allow for patient stabilization.  Both elective and emergent procedures may be minor or major procedures. -- Surgery - Risk Factors, Patient or Procedure:  Risk factors are those that are relevant to the patient and procedure.  Evidence based risk calculators may be used, but are not required, in assessing patient and procedure risk." },
        { label: 'Decision regarding hospitalisation or escalation of hospital level care', value: 'decision_hospitalisation', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Decision not to resuscitate or to deescalate care because of poor prognosis', value: 'decision_no_resuscitate', color: 'red', tooltipshow: false, tooltiptext: '?' },
        { label: 'Parenteral controlled substances', value: 'parenteral_substances', color: 'red', tooltipshow: false, tooltiptext: '?' }
    ]
]

//Chart D Options
const optionsChartD = [  
    [
        //99291
        { label: '99291', value: 'critical_care', color: 'blue', tooltipshow: true, tooltiptext: 'Critical Care (Minimum)' },
    ],
    [
        //99292
        { label: '99292', value: 'critical_care_extended', color: 'blue', tooltipshow: true, tooltiptext: 'Critical Care (Beyond Maximum)' },
    ],
]

//Providers
const providers = [
    'Ernest Sorini MD', 
    'Harry Aretakis MD', 
    'John Bauer MD', 
    'Walter Kaniefski MD', 
    'Susan Secrete NP', 
    'Robilyn Collins NP', 
    'Jennifer Driggers PA',
    'Vincent Tuzze PA',
    'Bertsche Debra PA'
]

const providersFiltered = ref(providers)

const providerPrimary = ref(0)
const providerSecondary = ref(0)

const finalLevelOptions = ref(levelMap)
const finalLevel = ref('No E&M')

const criticalCareOptions = ref(['99291'])
const criticalCareDefault = ref('99291')

const LevelOptions = ref(levelMap)

const modifierOptions = ref([
    '22', 
    '23', 
    '26', 
    '32', 
    '33', 
    '57', 
    '58', 
    '59', 
    '63'
])

const showCriticalCare = ref(false)
const showModifiers = ref(true)

const modifiers = ref([])

const notes = ref('')

const records = ref([
    {
      active: true,
      index: 0,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    },
    {
      active: false,
      index: 1,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    },
    {
      active: false,
      index: 2,
      level: 'No E&M',
      modifiers: [],
      notes: '',
      multiplier: 1
    }
])

const summaryTree = [ { label: '' } ]

class node {
    constructor(label, key, children = []) {
        this.label = label
        this.children = children
        this.key = key
    }
}

//Time based calculation guides
const criticalCareGuides = {
    government: {
        minimum: 30,
        maximum: 103,
        extended: 30
    },
    commercial: {
        minimum: 30,
        maximum: 74,
        extended: 30
    }
}

export default {
    setup () {
        return {
            step: ref(1),

            treeSelection,
            treeSelect: ref(false),
            treeKey,

            pseudoCodeSelect: ref(false),

            criticalCare,
            commercialCare,

            level,
            summaryTree,

            categoryOne,
            categoryOneBind,

            //Binding for tracking user item selection
            selections,

            //Level map
            levelMap,
            pseudoCodes,

            //Chart options
            optionsChartA,
            optionsChartB,

            //Chart B solutions matrix
            selectionMatrixChartB,

            //Chart C options categorised
            optionsChartC,

            //Chart D - critical care
            optionsChartD,

            providersFiltered,

            providerPrimary,
            providerSecondary,

            finalLevelOptions,
            finalLevel,

            criticalCareOptions,
            criticalCareDefault,

            showCriticalCare,
            showModifiers,

            LevelOptions,
            modifierOptions,
            modifiers,

            notes,

            records,

            //Filter provider list
            filterProviders (val, update, abort) {
                update(() => {
                    const needle = val.toLowerCase()
                    providersFiltered.value = providers.filter(v => v.toLowerCase().indexOf(needle) > -1)
                })
            },

            treeDeselect () {
                //Clear summary tree selection
                treeSelection.value = null;
            },

            usePseudoCode (val) {
                console.log("Pseudo code selection.")
                if (val == true) {
                    LevelOptions.value = pseudoCodes
                } else if (val == false) {
                    LevelOptions.value = levelMap
                }
            },

            //Establish Final Level
            establishFinalLevel () {
                if (finalLevel != '99292') {
                    showCriticalCare.value = false;
                } else {
                    showCriticalCare.value = true;
                }
                const codes = ["99281", "99282", "99283", "99284", "99285", "99291", "99292"];

                if (!codes.includes(finalLevel)) {
                    showModifiers.value = false;
                } else {
                    showModifiers.value = true;
                }
            },

            //Calculate E&M Level
            calculateLevel (val) {

                /*
                 ******************************
                 * Update Summary Tree when
                 * nodes are deselected
                 ******************************
                 */
                if (treeSelection.value != null) {
                    if (selections.value.indexOf(treeSelection.value) > -1) {
                        if (['critical_care', 'critical_care_extended'].indexOf(treeSelection.value) == -1) {
                            console.log("Selection Review: '" + treeSelection.value + "'")
                            selections.value.splice(selections.value.indexOf(treeSelection.value), 1)
                        }
                    } else if ([ "unique_source_review", "unique_test_review", "unique_test_ordering"].indexOf(treeSelection.value) > -1) {
                        //Object is Array, Array is Object (JSBS) 
                        categoryOne[treeSelection.value] = 0
                    }
                    //Clear tree selection and force tree update
                    treeSelection.value = null;
                    treeKey.value += 1;
                }

                /*
                 ******************************
                 * Critical Care Calculation
                 ******************************
                */

                //Load calculation parameters (government or commerical)
                const criticalCareGuide = commercialCare.value ? criticalCareGuides.commercial : criticalCareGuides.government;

                if (criticalCare.value >= criticalCareGuide.minimum) {
                    //Select "critical care" by adding it to the selections array, and change label value in dataset
                    selections.value.push("critical_care")
                    optionsChartD[0][0].label = '99291 x1'

                    if (criticalCare.value > criticalCareGuide.maximum) {
                        //Select "critical care extended" and change label to reflect "time multiplier"
                        //The changed label will be used for the summary tree as well
                        selections.value.push("critical_care_extended")
                        optionsChartD[1][0].label = '99292 x' + Math.ceil((criticalCare.value - criticalCareGuide.maximum) / criticalCareGuide.extended)

                    } else {
                        //Deselect "critical care extended" by removing it from the selections array, and revert to initial label value in dataaset
                        //The changed label will be used for the summary tree as well
                        if (selections.value.indexOf("critical_care_extended") > -1) {
                            selections.value.splice(selections.value.indexOf("critical_care_extended"), 1)
                            optionsChartD[1][0].label = '99292'
                        }
                    }
                } else {
                    //Deselect options and revert label changes
                    if (selections.value.indexOf("critical_care") > -1) {
                        selections.value.splice(selections.value.indexOf("critical_care"), 1)
                        optionsChartD[0][0].label = '99291'
                    }

                    if (selections.value.indexOf("critical_care_extended") > -1) {
                        selections.value.splice(selections.value.indexOf("critical_care_extended"), 1)
                        optionsChartD[1][0].label = '99292'
                    }
                }

                /*
                 ******************************
                 * E&M Level Calculation
                 ******************************
                 */
                let maximumA = 0
                let maximumB = 0
                let maximumC = 0
                let maximumD = ''

                //Clear summary tree
                while(summaryTree.length > 0) {
                    summaryTree.pop();
                }

                //"Zero state" for "chart b" combinations.
                let combinationB = ['NA',0,0,0,0,0,0,-1]

                let totality = []

                //Compile all available options. The order is an important part of calculating.
                let chartOptions = [    
                    optionsChartA,
                    optionsChartB,
                    optionsChartC,
                    optionsChartD
                ];

                let categoryNode = null; //Categories/level node for the summary tree

                //Iterate through chart groups
                for (let chart = 0; chart < chartOptions.length; chart++) {
                    let chartNode = new node(['Table A', 'Table B', 'Table C', 'Critical Care'][chart], "C" + chart)

                    //Iterate through option grouping (by category)
                    for (let category = 0; category < chartOptions[chart].length; category++) {
                        //Labelling Matrix
                        let breakdown = [
                            ['Straightforward', 'Limited','Minimal','Low','Moderate','High'],
                            ['Category One','Category Two','Category Three'],
                            ['Straightforward', 'Limited','Minimal','Low','Moderate','High'],
                            ['Critical Care', 'Critical Care Extended']
                        ]
                        
                        categoryNode = new node(breakdown[chart][category], "T" + chart + "L" + category)
                            
                        //Iterate through individual options/issues
                        for (let  procedure = 0; procedure < chartOptions[chart][category].length; procedure++) {
                            //Was the selection found in the dataset of available options?
                            if (selections.value.indexOf(chartOptions[chart][category][procedure].value) > -1) {

                                //Chart D - Critical Care (dataset element of index 3) must have children attached directly instead of being attached to a child node
                                (chart < 3 ? categoryNode : chartNode).children.push(new node(chartOptions[chart][category][procedure].label, chartOptions[chart][category][procedure].value))

                                //Store highest level selected for each chart
                                switch(chart) {
                                    //Chart A
                                    case 0:
                                        maximumA = category > maximumA ? category : maximumA;
                                        break;

                                    //Chart B (categories two and three)
                                    case 1:
                                        /*
                                         * Chart B has three levels which can be 
                                         * considered "category levels". The dataset
                                         * currently has two procedures in the second
                                         * level and one procedure in the third level.
                                         * We're recording if any of these selections
                                         * were made and storing this data in a special
                                         * "combination" array to be used later. We're
                                         * mapping the selections that are "grouped"
                                         * by category to the combination array. 
                                         * 
                                         * Procedure one of the second level is mapped
                                         * to array element 4. Procedure two of the 
                                         * second level is mapped to array element 5.
                                         * Procedure one of the third level is mapped
                                         * to array element 6. We store a value of "1"
                                         * to indicate that the selection was made (the
                                         * default value of the elements is 0).
                                         */
                                        combinationB[4 + ((category - 1) * 2) + procedure] = 1
                                        break;

                                    //Chart C
                                    case 2:
                                        maximumC = category > maximumC ? category : maximumC;
                                        break;

                                    //Chart D - CriticalCare
                                    case 3:
                                        maximumD = chartOptions[chart][category][procedure].value;
                                        break;
                                } 
                            } else if (chart == 1 && category == 0) {
                                /*
                                 * Chart B (category one)
                                 *
                                 * Here we map first level elements (of which there 
                                 * are three) to combination arrayelements 1, 2 and 3. 
                                 * This stands in opposition to second and third level 
                                 * elements, first level elements are integers that can 
                                 * be greater than 1.
                                 */
                                if (categoryOne[chartOptions[chart][category][procedure].value] > 0) {
                                    //Object is Array, Array is Object (JSBS)
                                    combinationB[1 + procedure] = categoryOne[chartOptions[chart][category][procedure].value];
                                    categoryNode.children.push(new node(categoryOne[chartOptions[chart][category][procedure].value] + " " + chartOptions[chart][category][procedure].label, chartOptions[chart][category][procedure].value))
                                }
                            }
                        }

                        //Collect loaded nodes for tree summary
                        if (categoryNode.children.length > 0) {
                            chartNode.children.push(categoryNode)
                        }
                    }

                    //Collect loaded nodes for tree summary
                    if (chartNode.children.length > 0) {
                        summaryTree.push(chartNode)
                    }
                }

                /* 
                 * Iterate through matrix (built from documentation) 
                 * and test "chart b" combination against each element
                 * of the matrix. Each element of the matrix represents
                 * the minimum threshold require for the "level" stored
                 * in that matrix element. This is why we iterate to the
                 * end. We need to test until we "confirm" that we cannot
                 * meet the minimum threshold.
                 */
                for (let combination = 0; combination < selectionMatrixChartB.length; combination++) {
                    let matchCount = 0
                    //Iterate and compare elements of the chart b combination and matrix element
                    for (let selected = 1; selected < combinationB.length - 1; selected++) {
                        if (combinationB[selected] >= selectionMatrixChartB[combination][selected]) {
                            matchCount++
                        }
                    }

                    //Record the maximum level only.
                    if (matchCount == 6) {
                        maximumB = selectionMatrixChartB[combination][7] > maximumB ? selectionMatrixChartB[combination][7] : maximumB;
                    }
                }

                totality = [maximumA, maximumB, maximumC]
                totality.sort()
                totality.reverse();


                /*******************************
                 * Update UI elements in final step
                 *******************************/

                //Reset elements
                records.value.forEach(function(record) {
                    record.active = false
                    record.level = 'No E&M'
                    record.modifiers = []
                    record.notes = ''
                });

                switch(maximumD) {
                    case '':
                        level.value = levelMap[totality[1]] //Overall level is second highest level
                        showCriticalCare.value = false
                        showModifiers.value = true

                        records.value[0].level = levelMap[totality[1]]
                        records.value[0].active = true
                        records.value[0].multiplier = 1
                        break;
                    
                    case 'critical_care':
                        level.value = '99291'
                        showCriticalCare.value = false
                        showModifiers.value = true


                        records.value[0].active = true
                        records.value[0].level = '99291'
                        records.value[0].multiplier = 1
                        break;

                    case 'critical_care_extended':
                        level.value = '99292'
                        showCriticalCare.value = true
                        showModifiers.value = false

                        records.value[0].active = true
                        records.value[0].level = '99291'
                        records.value[0].multiplier = 1

                        records.value[1].active = true
                        records.value[1].level = '99292'
                        records.value[1].multiplier = Math.ceil((criticalCare.value - criticalCareGuide.maximum) / criticalCareGuide.extended)
                        break;
                }

                console.log("E&M Calculation Complete")
                console.log("A Level: " + maximumA)
                console.log("B Level: " + maximumB)
                console.log("C Level: " + maximumC)
                console.log("D level: " + maximumD)
                console.log("Overall Level: " + level.value)

                finalLevel.value = level.value
            }
        }
    }
}
</script>
<template>
    <q-stepper
        v-model="step"
        vertical
        color="primary"
        animated
        header-nav
        flat
    >

        <q-step
            :name="1"
            title="Table A"
            caption="Number/Complexity of Problem Addressed-Nature of Presenting Problem"
            icon="description"
            :done="step > 1"
        >

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-green">
                    Minimal
                </q-banner>

                <br>

                <q-option-group
                    :options="optionsChartA[2]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" max-width="800px" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <!-- Low level procedures -->
            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-amber">
                    Low
                </q-banner>

                <br>

                <q-option-group
                    :options="optionsChartA[3]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" max-width="800px" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-orange">
                    Moderate
                </q-banner>
                <br>
                <q-option-group
                    :options="optionsChartA[4]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" max-width="800px" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-red">
                    High
                </q-banner>
                <br>
                <q-option-group
                    :options="optionsChartA[5]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2" max-width="800px" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <q-stepper-navigation>
                <q-btn @click="step = 2" color="primary" label="Continue" />
            </q-stepper-navigation>

        </q-step>

        <q-step
            :name="2"
            title="Table B"
            caption="Review and Analyzed Data"
            icon="description"
            :done="step > 2"
        >

            <q-banner inline-actions class="text-white bg-blue">
                Category One
            </q-banner>
            <div class="q-pa-md">
                <q-input
                    v-model="categoryOneBind.unique_source_review"
                    type="number"
                    style="max-width: 400px"
                    label = "Review of prior external note(s) from each unique source"
                    filled
                    @update:model-value="calculateLevel"
                    :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                />
            </div>

            <div class="q-pa-md">
                <q-input
                    v-model="categoryOneBind.unique_test_review"
                    type="number"
                    style="max-width: 400px"
                    label ="Review of the result(s) of each unique test"
                    @update:model-value="calculateLevel"
                    filled
                    :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                />
            </div>

            <div class="q-pa-md">
                <q-input
                    v-model="categoryOneBind.unique_test_ordering"
                    type="number"
                    style="max-width: 400px"
                    label ="Ordering of each unique test"
                    @update:model-value="calculateLevel"
                    filled
                    :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                />
            </div>

            <q-banner inline-actions class="text-white bg-blue">
                Category Two
            </q-banner>

            <div class="q-pa-md">
                <q-option-group
                    :options="optionsChartB[1]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                    >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <q-banner inline-actions class="text-white bg-blue">
                Category Three
            </q-banner>

            <div class="q-pa-md">
                <q-option-group
                    :options="optionsChartB[2]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <q-stepper-navigation>
                <q-btn @click="step = 3" color="primary" label="Continue" />
                <q-btn flat @click="step = 1" color="primary" label="Back" class="q-ml-sm" />
            </q-stepper-navigation>

        </q-step>

        <q-step
            :name="3"
            title="Table C"
            caption="Risk of complications and/or Morbidity or Morality of Patient Management"
            icon="description"
            :done="step > 3"
        >

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-green">
                    Minimal
                </q-banner>
                <br>
                <q-option-group
                    :options="optionsChartC[2]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                    >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-amber">
                    Low
                </q-banner>
                <br>
                <q-option-group
                    :options="optionsChartC[3]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                    >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-orange">
                    Moderate
                </q-banner>

                <br>
                
                <q-option-group
                    :options="optionsChartC[4]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                    >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <div class="q-pa-md">
                <q-banner inline-actions class="text-white bg-red">
                    High
                </q-banner>

                <br>
                <q-option-group
                    :options="optionsChartC[5]"
                    type="checkbox"
                    v-model="selections"
                    @update:model-value="calculateLevel"
                    >
                    <template v-slot:label="opt">
                        <div class="row items-center">
                            <span>{{ opt.label }}</span>
                            <q-tooltip class="bg-blue-grey text-body2 shadow-4" max-width="800px" anchor="bottom middle" self="top middle" :offset="[0, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                        </div>
                    </template>
                </q-option-group>
            </div>

            <q-stepper-navigation>
                <q-btn @click="step = 4" color="primary" label="Continue" />
                <q-btn flat @click="step = 2" color="primary" label="Back" class="q-ml-sm" />
            </q-stepper-navigation>

        </q-step>

        <!-- Panel which shows critical care options -->
        <q-step
            :name="4"
            title="Critical Care"
            icon="schedule"
            caption="Time Spent Administering Critical Care"
            :done="step > 4"
        >

            <br>

            <q-input
                v-model="criticalCare"
                type="number"
                style="max-width: 300px"
                label = "Critical care time (minutes)"
                filled
                @update:model-value="calculateLevel"
                :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
            />

            <q-checkbox v-model="commercialCare" @update:model-value="calculateLevel" label="Commercial Care" />

            <q-option-group
                :options="optionsChartD[0]"
                type="checkbox"
                v-model="selections"
                @update:model-value="calculateLevel"
            >
                <template v-slot:label="opt">
                    <div class="row items-center">
                        <span>{{ opt.label }}</span>
                        <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                    </div>
                </template>
            </q-option-group>

            <q-option-group
                :options="optionsChartD[1]"
                type="checkbox"
                v-model="selections"
                @update:model-value="calculateLevel"
            >
                <template v-slot:label="opt">
                    <div class="row items-center">
                        <span>{{ opt.label }}</span>
                        <q-tooltip class="bg-blue-grey text-body2" :offset="[10, 10]" v-if="opt.tooltipshow">{{ opt.tooltiptext }}</q-tooltip>
                    </div>
                </template>
            </q-option-group>

            <q-stepper-navigation>
                <q-btn @click="step = 5" color="primary" label="Continue" />
                <q-btn flat @click="step = 3" color="primary" label="Back" class="q-ml-sm" />
            </q-stepper-navigation>
        </q-step>

        <!-- Panel which shows summary of everything selected in previous steps -->
        <q-step
            :name="5"
            title="Summary"
            icon="playlist_add_check"
            caption="Overview of Selections and Calculated Level"
            :done="step > 5"
        >
            <!-- Summary tree is built during in level calculation function -->
            <q-tree
                :nodes="summaryTree"
                node-key="key"
                default-expand-all
                v-model:selected="treeSelection"
                @update:selected="treeSelect = true"
                :key="treeKey"
            />
            <q-chip square>
                <q-avatar icon="equalizer" color="red" text-color="white" />
                {{ level }}
            </q-chip>

            <q-stepper-navigation>
                <q-btn @click="step = 6" color="primary" label="Finish" />
                <q-btn flat @click="step = 4" color="primary" label="Back" class="q-ml-sm" />
            </q-stepper-navigation>
        </q-step>

        <!-- Final panel which shows the data that will be submitted -->
        <q-step
            :name="6"
            title="Final"
            icon="description"
            caption="Levels, modifiers and providers."
        >
            <div class="q-pa-md row items-start q-gutter-md">
                <!-- We load this one only when there are critical time considerations and the level is 99292. This displays 99291.-->

                <q-card class="my-card" flat bordered style="width: 280px; min-height: 420px;">
                    <q-card-section style="min-height: 75px;">
                        <div class="row items-center no-wrap">
                            <div class="col">
                                <q-item-section>
                                    <q-item-label>Level</q-item-label>
                                    <q-item-label caption>Level and Modifiers</q-item-label>
                                </q-item-section>
                            </div>
                        
                            <div class="col-auto">
                                <q-btn color="grey-7" round flat icon="edit_note">
                                <q-menu cover auto-close>
                                    <q-list>
                                    <q-item clickable @click="pseudoCodeSelect = true">
                                        <q-item-section>Pseudo Code</q-item-section>
                                    </q-item>
                                    </q-list>
                                </q-menu>
                                </q-btn>
                            </div>
                        </div>
                    </q-card-section>


                    <q-separator />

                    <q-card-section>
                        <q-select
                            filled
                            v-model="records[0].level"
                            use-input
                            hide-selected
                            fill-input
                            input-debounce="0"
                            :options="LevelOptions"
                            hint="E&M Level"
                            >
                            <template v-slot:no-option>
                                <q-item>
                                <q-item-section class="text-grey">
                                    No results
                                </q-item-section>
                                </q-item>
                            </template>
                        </q-select>
                        <q-input
                            v-model="records[0].multiplier"
                            type="number"
                            style="max-width: 80px"
                            hint="Multiplier"
                            filled
                            :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                        />
                    </q-card-section>

                    <q-card-section  class="bg-grey-2" style="margin-left: 14px; margin-right: 14px; min-height: 95px; border-radius: 4px;">
                        <q-chip v-show=false></q-chip>
                        <q-chip color="red" square text-color="white" v-for="mod in records[0].modifiers">{{ mod }}</q-chip>
                        <q-popup-edit v-model="records[0].modifiers" v-slot="scope">
                            <q-select
                                v-model="records[0].modifiers"
                                multiple
                                :options="modifierOptions"
                                use-chips
                                stack-label
                                label="Select or remove modifiers"
                            />
                        </q-popup-edit>
                    </q-card-section>
                    <q-item-section style="margin-left: 24px; margin-top: 8px;">
                        <q-item-label caption>Modifiers</q-item-label>
                    </q-item-section>
                </q-card>


                <q-card class="my-card" flat bordered style="width: 280px; min-height: 420px;">

                    <q-card-section>
                        <div class="row items-center no-wrap">
                            <q-item-section>
                                <q-item-label>Providers</q-item-label>
                                <q-item-label caption>Primary and Secondary Providers</q-item-label>
                            </q-item-section>
                        </div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section>
                        <q-select
                            filled
                            v-model="providerPrimary"
                            use-input
                            hide-selected
                            fill-input
                            input-debounce="0"
                            :options="providersFiltered"
                            @filter="filterProviders"
                            hint="Type to filter primary providers"
                            style="width: 250px; padding-bottom: 32px"
                            >
                            <template v-slot:no-option>
                                <q-item>
                                <q-item-section class="text-grey">
                                    No results
                                </q-item-section>
                                </q-item>
                            </template>
                        </q-select>

                        <br/>

                        <q-select
                            filled
                            v-model="providerSecondary"
                            use-input
                            hide-selected
                            fill-input
                            input-debounce="0"
                            :options="providersFiltered"
                            @filter="filterProviders"
                            hint="Type to filter secondary providers"
                            style="width: 250px; padding-bottom: 32px"
                            >
                            <template v-slot:no-option>
                                <q-item>
                                <q-item-section class="text-grey">
                                    No results
                                </q-item-section>
                                </q-item>
                            </template>
                        </q-select>
                    </q-card-section>
                </q-card>

                <q-card class="my-card" flat bordered style="width: 280px; min-height: 420px;">
                    <q-card-section>
                        <div class="row items-center no-wrap">
                            <q-item-section>
                                <q-item-label>Notes</q-item-label>
                                <q-item-label caption>Coding and Override Notes</q-item-label>
                            </q-item-section>
                        </div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section>
                        <q-input
                            v-model="notes"
                            filled
                            type="textarea"
                        />
                    </q-card-section>
                </q-card>


            <q-card class="my-card" flat bordered style="width: 280px; min-height: 460px;" v-if="records[1].active">
                <q-card-section style="min-height: 75px;">
                    <div class="row items-center no-wrap">
                        <div class="col">
                        <q-item-section>
                            <q-item-label>Level</q-item-label>
                            <q-item-label caption>Level and Modifiers</q-item-label>
                        </q-item-section>
                    </div>
                    
                </div>
                </q-card-section>

                <q-separator />

                <q-card-section>
                    <q-select
                        filled
                        v-model="records[1].level"
                        use-input
                        hide-selected
                        fill-input
                        input-debounce="0"
                        :options="LevelOptions"
                        hint="E&M Level"
                        >
                        <template v-slot:no-option>
                            <q-item>
                            <q-item-section class="text-grey">
                                No results
                            </q-item-section>
                            </q-item>
                        </template>
                    </q-select>
                    <q-input
                        v-model="records[1].multiplier"
                        type="number"
                        style="max-width: 80px"
                        hint="Multiplier"
                        filled
                        :rules="[val => val >= 0 && val <= 1000 || 'Entry is invalid.']"
                    />
                </q-card-section>
            </q-card>
            </div>  
        </q-step>

    </q-stepper>

    <!-- Node removal warning dialogue. -->
    <q-dialog v-model="treeSelect" seamless transition-show="flip-down" transition-hide="flip-up">
        <q-card class="bg-red text-white shadow-24" style="width: 300px">
            <q-card-section>
                <div class="text-h6">Warning!</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
                Are you sure you want to remove this selection?
            </q-card-section>

            <q-card-actions align="right" class="bg-white text-primary">
                <q-btn flat label="No" color="primary" v-close-popup @click="treeDeselect" />
                <q-btn flat label="Yes" color="primary" v-close-popup @click="calculateLevel" />
            </q-card-actions>
        </q-card>
    </q-dialog>

    <!-- Pseudo code warning dialogue. -->
    <q-dialog v-model="pseudoCodeSelect" seamless transition-show="flip-down" transition-hide="flip-up">
        <q-card class="bg-red text-white shadow-24" style="width: 300px">
            <q-card-section>
                <div class="text-h6">Warning!</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
                Do you want to use a pseudo code?
            </q-card-section>

            <q-card-actions align="right" class="bg-white text-primary">
                <q-btn flat label="No" color="primary" v-close-popup @click="usePseudoCode(false)"/>
                <q-btn flat label="Yes" color="primary" v-close-popup @click="usePseudoCode(true)" />
            </q-card-actions>
        </q-card>
    </q-dialog>

</template>