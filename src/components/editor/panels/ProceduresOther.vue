<script>
import { ref } from 'vue'

export default {
  setup () {
    return {
      step: ref(1),
      procedureOthers: ref([])
    }
  }
}
</script>
<template>
    <div class="q-pa-md">
      <q-stepper
        v-model="step"
        vertical
        ref="stepper"
        color="primary"
        animated
      >
        <q-step :name="1" title="G9744" caption="Patient not eligible due to active diagnosis of hypertension." prefix="1" :done="step > 1">
        <q-toggle v-model="procedureOthers[0]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Screening for high blood pressure not documented.">
          <q-card>
            <q-card-section>
              Screening for high blood pressure not documented. Patient not eligible for high blood pressure screening (denominator exclusion).
              <br/>
              Patient has an active diagnosis of hypertension starts prior to the current encounter.
            </q-card-section>
          </q-card>
        </q-expansion-item>

        </q-step>
  
        <q-step :name="2" title="G8783" caption="Normal blood pressure reading documented; follow up not required." prefix="2" :done="step > 2">
        <q-toggle v-model="procedureOthers[1]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>

        </q-step>
  
        <q-step :name="3" title="G8950" caption="Prehypertensive or hypertensive blood pressure reading document, and the indicated follow up is documented." prefix="3" :done="step > 3">
        <q-toggle v-model="procedureOthers[2]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>
        </q-step>
  
        <q-step :name="4" title="G9745" caption="Documented reason for not screening or recommending a follow up for high blood pressure." prefix="4" :done="step > 4">
        <q-toggle v-model="procedureOthers[3]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>
        </q-step>

        <q-step :name="5" title="G9785" caption="Blood pressure reading not documented; reason not given." prefix="5" :done="step > 5">
        <q-toggle v-model="procedureOthers[4]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>
        </q-step>

        <q-step :name="6" title="G8952" caption="Prehypertensive or hypertensive blood pressure reading documented. Indicated follow up not documented; reason not given." prefix="6" :done="step > 6">
        <q-toggle v-model="procedureOthers[5]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>
        </q-step>

        <q-step :name="7" title="N/A" caption="Data completeness not met; the quality data code or equivalent was not submitted." prefix="7" :done="step > 7">
        <q-toggle v-model="procedureOthers[6]" checked-icon="check" color="red" unchecked-icon="clear"/>
        <q-expansion-item expand-separator label="Nunc augue ante, cursus at lectus a, commodo vulputate ante.">
          <q-card>
            <q-card-section>
              Nunc augue ante, cursus at lectus a, commodo vulputate ante. Donec ultrices neque dolor, sit amet finibus mi finibus vel. Etiam facilisis mollis euismod. Nam fringilla convallis nisi, finibus auctor felis. Fusce sit amet dignissim erat, nec vulputate nisl. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed sit amet consectetur nibh. Quisque sit amet volutpat metus. Praesent condimentum leo non aliquet viverra. Nullam rhoncus, nunc ut pulvinar interdum, odio mi vestibulum est, gravida venenatis dolor diam et lectus. Curabitur id turpis tortor. Quisque suscipit mauris diam, vitae lobortis diam viverra sit amet. Phasellus gravida orci felis, sed laoreet mauris blandit id. 
            </q-card-section>
          </q-card>
        </q-expansion-item>
        </q-step>

        <q-step :name="8"
          title="Summary"
          caption="Confirmation of selection made."
          prefix="8"
          :done="step > 8"
        >
          ToDo: Display selection.
        </q-step>
  
        <template v-slot:navigation>
          <q-stepper-navigation>
            <q-btn @click="$refs.stepper.next()" color="primary" :label="step === 8 ? 'Finish' : 'Continue'" />
            <q-btn v-if="step > 1" flat color="primary" @click="$refs.stepper.previous()" label="Back" class="q-ml-sm" />
          </q-stepper-navigation>
        </template>
      </q-stepper>
    </div>
  </template>