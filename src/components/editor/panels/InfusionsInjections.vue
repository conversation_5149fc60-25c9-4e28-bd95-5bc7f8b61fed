<script>
  import { ref } from 'vue'
  import { useEncounterStore } from '@/stores/encounter'
  import { useCodexStore } from '@/stores/codex'
  const tableTracker = ref(0);

  const drugs = [
          'Normal Saline', 
          'Keotorolac',
          'Morphine', 
          'Magnesium Sulfate',
          'Meropenem',
          'Paclitaxel'
  ]

  const procedureCodeModifiers = [
          '22', 
          '23', 
          '26', 
          '32', 
          '33', 
          '57', 
          '58', 
          '59', 
          '63'
  ]

  const types = [
    {
      value: 'Theraputic Hydration',
      description: 'Theraputic Hydration'
    },
    { 
      value: 'Medication IV Push',
      description: 'Medication IV Push'
    },
    { 
      value: 'Medication IV Infusion',
      description: 'Medication IV Infusion'
    },
    { 
      value: 'Chemotherapy',
      description: 'Chemotherapy'
    }
  ]

  const typesFiltered = ref(types)
  
  const columns = [
    { name: 'selected', align: 'left', field: 'selected'},
    { name: 'type', align: 'left', label: 'Type', field: 'type' },
    { name: 'site', align: 'left', label: 'Site', field: 'site' },
    { name: 'drugs', align: 'left', label: 'Drugs', field: 'drugs' },
    { name: 'date', align: 'left', label: 'Date', field: 'date' },
    { name: 'start', align: 'left', label: 'Start', field: 'start' },
    { name: 'stop', align: 'left', label: 'Stop', field: 'stop' },
    { name: 'duration', align: 'left', label: 'Duration', field: 'duration' },
    { name: 'nextDay', align: 'left', label: 'Next Day', field: 'nextDay' },
    { name: 'department', align: 'left', label: 'Department', field: 'department' },
    { name: 'procedure', align: 'left', label: 'Procedures and Modifiers', field: 'procedure' }
  ]

  export default {
    setup () {

      const encounter = useEncounterStore()
      const codex = useCodexStore()
      const loading = ref(false)
      const rowsFiltered = ref([...encounter.InfusionsInjections])
      const typesFiltered = ref([types])
      const sitesFiltered = ref([...codex.iniSites])
      const recordRelocate = ref(-1)

      return {
        encounter,
        codex,
        
        loading,
        columns,
        rowsFiltered,
        recordRelocate,
        tableTracker,
        typesFiltered,
        sitesFiltered,
        procedureCodeModifiers,
        drugs,

        //Filter types by name or description
        filterTypes (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            let typesRedux = []
            for (let i = 0; i < types.length; i++) {
              if (types[i].value.toLowerCase().indexOf(needle) > -1 || types[i].description.toLowerCase().indexOf(needle) > -1) {
                typesRedux.push(types[i])
              }
            }
            typesFiltered.value = typesRedux
          })
        },

        //Filter types by name or description
        filterSites (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            let sitesRedux = []
            for (let i = 0; i < codex.iniSites.length; i++) {
              if (codex.iniSites[i].value.toLowerCase().indexOf(needle) > -1 || codex.iniSites[i].description.toLowerCase().indexOf(needle) > -1) {
                sitesRedux.push(codex.iniSites[i])
              }
            }
            sitesFiltered.value = sitesRedux
          })
        },

        addRow(){
          loading.value = true
          let indexNew = encounter.InfusionsInjections.length
          let recordBlank = {
                index: indexNew,
                selected: false,
                date: '2024-12-01',
                type: 'Theraputic Hydration',
                site: 'Antecubital Right',
                drugs: ['Normal Saline'],
                start:'01:00',
                stop: '02:00',
                duration: 60,
                durationActual: 60,
                nextDay: false,
                department: 'FGHED EMERGENCY SERVICES - 2600',
                procedures: [{ procedure: 96360, quantity: 1, modifiers: ['22']}],
                initialCode: false,
                charges: false
              }
          rowsFiltered.value.push(encounter.InfusionsInjections[encounter.InfusionsInjections.push(recordBlank) - 1])
          encounter.iniCalculate()
          loading.value = false
        },

        removeRow(){
          loading.value = true
          //Marked I&I entries will be removed. New array will be returned.
          rowsFiltered.value = encounter.removeInfusionInjection();
          loading.value = false
        },
      }
    }
  }
</script>
<template>
    <q-table flat bordered
             title="Infusions & Injections" 
             :rows="rowsFiltered"
             :columns="columns"
             row-key="index"
             :key="tableTracker"
             :loading="loading"
             wrap-cells
             >
      <template v-slot:top>
        <q-btn color="primary" :disable="loading" label="Add row" @click="addRow" />
        <q-btn class="q-ml-sm" color="primary" :disable="loading" label="Remove Row" @click="removeRow" />
      </template>
      
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td colspan="100%" style="height: 5px; padding: 0px; border: none;">
          </q-td>
        </q-tr>
        <q-tr :props="props">
          <q-td auto-width>
            <q-checkbox v-model="props.row.selected" />
          </q-td>
          
          <q-td key="type" :props="props">
            {{ props.row.type }}
            <q-popup-edit v-model="props.row.type" title="Select Type" buttons v-slot="scope">
              <q-select
                filled
                v-model="props.row.type"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :options="typesFiltered"
                @filter="filterTypes"
                option-value="value"
                option-label="value"
                emit-value
                map-options
                @update:model-value="encounter.iniCalculate()"

                hint="Type to filter"
                style="width: 250px; padding-bottom: 32px"
              >

                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section>
                      <q-item-label>{{ scope.opt.value }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </q-popup-edit>
          </q-td>

          <q-td key="site" :props="props">       
            {{ props.row.site }}
            <q-popup-edit v-model="props.row.site" title="Select Site" buttons v-slot="scope">
              <q-select
                filled
                v-model="props.row.site"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :options="sitesFiltered"
                @filter="filterSites"
                option-value="value"
                option-label="value"
                emit-value
                map-options
                @update:model-value="encounter.iniCalculate()"

                hint="Type to filter"
                style="width: 250px; padding-bottom: 32px"
              >

                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section>
                      <q-item-label>{{ scope.opt.value }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </q-popup-edit>
          </q-td>

          <q-td key="drugs" :props="props" style="width: 140px">
          <q-chip v-show=false></q-chip>
          <q-chip color="red" square text-color="white" v-for="drug in props.row.drugs">{{ drug }}</q-chip>
          <q-popup-edit v-model="props.row.mods" v-slot="scope">
            <q-select
              v-model="props.row.drugs"
              multiple
              :options="drugs"
              use-chips
              stack-label
              label="Select or remove drugs"
            />
          </q-popup-edit>
        </q-td>

        <q-td key="date" :props="props">       
            {{ props.row.date}}
          </q-td>
      
          <q-td key="start" :props="props">       
            <q-card flat bordered class="my-card" style="text-align: left">
              <q-card-section>
                {{ props.row.start }}
                <q-popup-edit v-model="props.row.start" title="Update Start Time" buttons label-set="Save" label-cancel="Close" v-slot="scope" @update:model-value="encounter.iniCalculate()">
                  <q-time v-model="scope.value" format24h />
                </q-popup-edit>
              </q-card-section>
            </q-card>
          </q-td>

          <q-td key="stop" :props="props">       
            <q-card flat bordered class="my-card" style="text-align: left">
              <q-card-section>
                {{ props.row.stop }}
                <q-popup-edit v-model="props.row.stop" title="Update Start Time" buttons label-set="Save" label-cancel="Close" v-slot="scope" @update:model-value="encounter.iniCalculate()">
                  <q-time v-model="scope.value" format24h />
                </q-popup-edit>
              </q-card-section>
            </q-card>
          </q-td>

          <q-td key="duration" :props="props" style="width: 125px">

            {{ props.row.durationActual == props.row.duration ? props.row.durationActual + ' Min.': props.row.durationActual + ' (' + props.row.duration + ') Min.' }}

          </q-td>

          <q-td style="width: 100px">
            <q-checkbox v-model="props.row.nextDay" @update:model-value="encounter.iniCalculate()"/>
          </q-td>

          <q-td key="department" :props="props">       
            <q-card flat bordered class="my-card" style="text-align: left">
              <q-card-section>
                {{ props.row.department }}
              </q-card-section>
            </q-card>
          </q-td>

          <q-td key="procedure" :props="props" style="width: 200px">
            <div v-if="props.row.procedures" v-for="(proc, idx) in props.row.procedures">
              <div style="min-width: 250px">
                <q-chip v-show=false></q-chip>
                <q-chip 
                  
                  :key="idx"
                  :color="idx === 0 ? (props.row.initialCode ? 'red' : 'grey') : 'blue'"
                  square 
                  text-color="white"
                  class="q-ma-xs"
                >
                {{ proc.procedure }} ×{{ proc.quantity }}

                  <q-popup-edit v-model="proc.modifiers" v-slot="scope" style="width: 250px">
                    <q-select
                      v-model="proc.modifiers"
                      multiple
                      :options="procedureCodeModifiers"
                      use-chips
                      stack-label
                      :label="`Select Modifiers for ${proc.procedure}`"
                    />
                  </q-popup-edit>

                </q-chip>

                <q-chip 
                  v-for="(mod, modIdx) in proc.modifiers" 
                  :key="modIdx"
                  square
                  color="orange"
                  text-color="white"
                  class="q-ma-xs"
                  >
                    {{ mod }}
                </q-chip>
              </div>

            </div>
            <span v-else>N/A</span>
          </q-td>

        </q-tr>
      </template>
    </q-table>
</template>