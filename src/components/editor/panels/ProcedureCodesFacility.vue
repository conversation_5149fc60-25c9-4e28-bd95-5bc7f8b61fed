<script>
  import { ref } from 'vue'
  import { useEncounterStore } from '@/stores/encounter'

  const providers = [
          '<PERSON>', 
          '<PERSON>', 
          '<PERSON>', 
          '<PERSON>', 
          '<PERSON>', 
          '<PERSON><PERSON><PERSON>', 
          '<PERSON>',
          '<PERSON>',
          'Bertsche Debra PA'
  ]

  const providersFiltered = ref(providers)

  const procedureCodeModifiers = [
          '22', 
          '23', 
          '26', 
          '32', 
          '33', 
          '57', 
          '58', 
          '59', 
          '63'
  ]

  const procedureCodes = [
    {
      value: '20612',
      description: 'Asp and/or inj of ganglion c'
    },
    {
      value: '20615',
      description: 'Asp bone cyst/pc'
    }, 
    {
      value: '21310',
      description: 'Nasal fracture wo manip/pc'
    },
    {     
      value: '21315',
      description: 'Nasal fracture wo stabiliz/pc'
    },
    { 
      value: '21320',
      description: 'Nasal fracture w stabiliz/pc'
    }, 
    {
      value: '21325',
      description: 'Open uncomp nasal fracture/pc'
    }, 
    {
      value: '21337',
      description: 'Nasal septal fx wo stabiliz/pc'
    },
    {      
      value: '37195',
      description: 'Thrombolysis (cerebral) by iv'
    },
    {
      value: '37618',
      description: 'Ligation; major artery; extremi'
    },
    {
      value: '38300',
      description: 'Drng lymph node ab sim/pc'
    },
    {
      value: '40652',
      description: 'Rpr lip To 1/2 height/pc'
    },
    {
      value: '40654',
      description: 'Rep lip full thickness <1/2 ve'
    },
    {
      value: '40800',
      description: 'Drng abc mouth sim/pc'
    },
    {
      value: '40800',
      description: 'Drng abc mouth comp/pc'
    }
  ]

  const procedureCodesFiltered = ref(procedureCodes)

  const columnsProceduresFacility = [
        { name: 'selected', align: 'left', label: ' ', field: 'selected', style: 'width: 5px'},
        { name: 'code', align: 'left', label: 'Code', field: 'code', sortable: true },
        { name: 'description', align: 'left', label: 'Description', field: 'description' },
        { name: 'mods', label: 'Modifiers', field: 'mods', style: 'width: 40px' },
        { name: 'q', label: 'Qty', field: 'q', align: 'left', style: 'width: 5px'},
        { name: 'date', label: 'Date', field: 'date' },
        { name: 'bp', label: 'Billing Provider', field: 'bp' },
        { name: 'sp', label: 'Secondary Provider', field: 'sp', sortable: true, sort: (a, b) => parseInt(a, 10) - parseInt(b, 10) },
        { name: 'diagnosis', align: 'left', label: 'Diagnosis', field: 'diagnosis' }
    ]

  export default {
    setup () {
      const encounter = useEncounterStore()
      const loading = ref(false)
      const providerWarning = ref(false)
      const recordsFiltered = ref([...encounter.procedureCodesFacility])
      const diagnosisCodesFiltered = ref([...encounter.diagnosisCodes])

      return {
        encounter,

        providerWarning,

        loading,
        columnsProceduresFacility,
        recordsFiltered,
        procedureCodeModifiers,
        procedureCodesFiltered,
        providersFiltered,

        diagnosisCodesFiltered,

        filterProviders (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            providersFiltered.value = providers.filter(v => v.toLowerCase().indexOf(needle) > -1)
          })
        },

        //Filter procedure code list by code or description. Matching starts with the first character entered by the user.
        filterProcedureCodes (val, update, abort) {
          update(() => {
            const needle = val.toLowerCase()
            let procedureCodesRedux = []
            for (let i = 0; i < procedureCodes.length; i++) {
              if (procedureCodes[i].value.toLowerCase().indexOf(needle) > -1 || procedureCodes[i].description.toLowerCase().indexOf(needle) > -1) {
                procedureCodesRedux.push(procedureCodes[i])
              }
            }
            procedureCodesFiltered.value = procedureCodesRedux
          })
        },

        removeRow () {
          loading.value = true
          setTimeout(() => {
            //Marked procedure codes will be removed. New array will be returned.
            recordsFiltered.value = encounter.removeProcedureFacility();
            loading.value = false
          }, 500)
        },

        addRow () {
          loading.value = true
          let indexNew = encounter.procedureCodesFacility.length
          let recordBlank = {
              selected: false,
              index: indexNew,
              type: 'CPT',
              mods: [],
              code: '',
              description: '',
              q: 1,
              bp: '',
              sp: '',
              date: '2023/05/04'
          }
          setTimeout(() => {
            recordsFiltered.value.push(encounter.procedureCodesFacility[encounter.procedureCodesFacility.push(recordBlank) - 1])
            loading.value = false
          }, 500)
        },

        duplicateRow () {
          loading.value = true
          setTimeout(() => {
            for (let i = encounter.procedureCodesFacility.length - 1; i >= 0; i--) {
              if (encounter.procedureCodesFacility[i].selected == true) {
                encounter.procedureCodesProfessional.push(encounter.procedureCodesFacility[i])
              }
            }
            loading.value = false
          }, 500)
        },

        updateProvider (val) {
          loading.value = true

          setTimeout(() => {
            for (let i = 0; i < encounter.procedureCodesFacility.length; i++) {
              if (encounter.procedureCodesFacility[i].code == val) {
                if (encounter.procedureCodesFacility[i].bp == encounter.procedureCodesFacility[i].sp) {
                  providerWarning.value = true
                  console.log("Provider values should not be equal.")
                }
              }
            }
            loading.value = false
          }, 500)
        },

        updateDescription (val) {
          loading.value = true

          let description = ''

          setTimeout(() => {
            for (let i = 0; i < procedureCodes.length; i++) {
              if (procedureCodes[i].value == val) {
                description = procedureCodes[i].description;
                break;
              }
            }

            for (let i = 0; i < encounter.procedureCodesFacility.length; i++) {
              if (encounter.procedureCodesFacility[i].code == val) {
                if (encounter.procedureCodesFacility[i].description != description) {
                  encounter.procedureCodesFacility[i].description = description;
                }
                if (encounter.procedureCodesFacility[i].bp == encounter.procedureCodesFacility[i].sp) {
                  console.log("Provider values should not be equal.")
                }
              }
            }
            loading.value = false
          }, 500)
        }
      }
    }
  }
</script>

<template>           
  <q-table 
    flat bordered title="CPT" 
    :rows="recordsFiltered"
    :columns="columnsProceduresFacility"
    row-key="index"
    binary-state-sort
    :loading="loading"
    wrap-cells
  >
    <template v-slot:body="props">
      <q-tr :props="props">

        <q-td auto-width>
          <q-checkbox v-model="props.row.selected" />
        </q-td>

        <q-td key="code" :props="props">
          {{ props.row.code }}
          <q-popup-edit v-model="props.row.code" title="Select procedure code" buttons v-slot="scope">
            <q-select
              filled
              v-model="props.row.code"
              @update:model-value="updateDescription"
              use-input
              hide-selected
              fill-input
              input-debounce="0"
              :options="procedureCodesFiltered"
              @filter="filterProcedureCodes"
              option-value="value"
              option-label="value"
              emit-value
              map-options

              hint="Type to filter"
              style="width: 250px; padding-bottom: 32px"
            >

              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section>
                    <q-item-label>{{ scope.opt.value }}</q-item-label>
                    <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </q-popup-edit>
        </q-td>

        <q-td key="description" :props="props" style="width: 160px">       
          <q-card flat bordered class="my-card" style="text-align: left">
            <q-card-section>
              {{ props.row.description }}
            </q-card-section>
          </q-card>
        </q-td>

        <q-td key="mods" :props="props" style="width: 140px">
          <q-chip v-show=false></q-chip>
          <q-chip color="red" square text-color="white" v-for="mod in props.row.mods">{{ mod }}</q-chip>
          <q-popup-edit v-model="props.row.mods" v-slot="scope">
            <q-select
              v-model="props.row.mods"
              multiple
              :options="procedureCodeModifiers"
              use-chips
              stack-label
              label="Select or remove modifiers"
            />                   
          </q-popup-edit>
        </q-td>

        <q-td key="q" :props="props">
          {{ props.row.q }}
          <q-popup-edit v-model="props.row.q" title="Update quantity" buttons label-set="Save" label-cancel="Close" v-slot="scope">
            <q-input type="number" v-model="scope.value" dense autofocus hint="Select value between 1 and 10" :rules="[val => val >= 1 && val <= 1000 || 'Entry is invalid.']"/>
          </q-popup-edit>
        </q-td>

        <q-td key="date" :props="props">
          {{ props.row.date }}
          <q-popup-edit v-model="props.row.date" v-slot="scope">
            <q-date
              v-model="props.row.date"
              landscape
            />
          </q-popup-edit>               
        </q-td>

        <q-td key="bp" :props="props">{{ props.row.bp }}
          <q-popup-edit v-model="props.row.bp" title="Select provider" v-slot="scope">
            <q-select
              filled
              v-model="props.row.bp"
              @update:model-value="updateProvider(props.row.code)"
              use-input
              hide-selected
              fill-input
              input-debounce="0"
              :options="providersFiltered"
              @filter="filterProviders"
              hint="Type to filter"
              style="width: 250px; padding-bottom: 32px"
            >

              <template v-slot:no-option>
                <q-item>
                  <q-item-section class="text-grey">
                    No results
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </q-popup-edit>
        </q-td>

        <q-td key="sp" :props="props">{{ props.row.sp }}
          <q-popup-edit v-model="props.row.sp" title="Select provider" v-slot="scope">
            <q-select
              filled
              v-model="props.row.sp"
              @update:model-value="updateProvider(props.row.code)"
              use-input
              hide-selected
              fill-input
              input-debounce="0"
              :options="providersFiltered"
              @filter="filterProviders"
              hint="Type to filter"
              style="width: 250px; padding-bottom: 32px"
            >

              <template v-slot:no-option>
                <q-item>
                  <q-item-section class="text-grey">
                    No results
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </q-popup-edit>                            
        </q-td>

        <q-td key="diagnosis" :props="props">{{ props.row.diagnosis }}
          <q-popup-edit v-model="props.row.diagnosis" title="Select diagnosis" v-slot="scope">
            <q-select
              filled
              v-model="props.row.diagnosis"
              @update:model-value="encounter.updateAssociations"
              use-input
              hide-selected
              fill-input
              input-debounce="0"
              :options="diagnosisCodesFiltered"
              @filter="filterDiagnosisCodes"
              hint="Type to filter"
              style="width: 250px; padding-bottom: 32px"
              option-value="code"
              option-label="code"
              emit-value
              map-options
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section>
                    <q-item-label>{{ scope.opt.code }}</q-item-label>
                    <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </q-popup-edit>                            
        </q-td>

      </q-tr>
    </template>

    <!-- Add and remove buttons -->
    <template v-slot:top>
      <q-btn class="q-ml-sm" color="primary" :disable="loading" label="Add Row" @click="addRow" />
      <q-btn class="q-ml-sm" color="primary" :disable="loading" label="Remove Row" @click="removeRow" />
      <q-btn class="q-ml-sm" color="primary" :disable="loading" label="Duplicate Row" @click="duplicateRow" />
      <q-space />
    </template>
  </q-table>

  <!-- Providers warning dialogue. -->
  <q-dialog v-model="providerWarning" seamless transition-show="flip-down" transition-hide="flip-up">
    <q-card class="bg-red text-white shadow-24" style="width: 300px">
      <q-card-section>
        <div class="text-h6">Warning!</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        The billing and secondary providers cannot be the same.
      </q-card-section>

      <q-card-actions align="right" class="bg-white text-primary">
        <q-btn flat label="Okay" color="primary" v-close-popup/>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>