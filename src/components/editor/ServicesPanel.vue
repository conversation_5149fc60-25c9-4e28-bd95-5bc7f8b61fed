<script>
  import { ref } from 'vue'

  export default {
    setup() {
        return {
            tab: ref('tab'),
            tabFacilitySub: ref('tabFacilitySub'),
            tabProfessionalSub: ref('tabProfessionalSub'),
            splitterModel: ref(10)
        };
    }
}
</script>

<template>
    <div class="q-pa-md services">
      <div class="q-gutter-y-md" style="max-width: 1500px;">
        <q-card flat bordered>
          <q-tabs
            v-model="tab"
            dense
            class="text-grey"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator
          >
            <q-tab name="professional_em" icon="face" label="Pro. E&M" />
            <q-tab name="facility_em" icon="corporate_fare" label="Facility E&M" />
            <q-tab name="observation_stays" icon="single_bed" label="Observation Stays" />
            <q-tab name="infusions_injections" icon="vaccines" label="Infusions & Injections" />
            <q-tab name="x_ray" icon="monitor" label="X-Ray" />
            <q-tab name="ultrasound" icon="spatial_audio" label="Ultrasound" />
            <q-tab name="laceration_repair" icon="personal_injury" label="Laceration Repair" />
            <q-tab name="diagnosis_codes" icon="assignment" label="Diagnoses" />
            <q-tab name="mips" icon="medical_services" label="MIPS" />
            <q-tab name="hcpcs" icon="emergency" label="HCPCS" />
          </q-tabs>
  
          <q-separator />
  
          <q-tab-panels v-model="tab" animated>
            <q-tab-panel name="professional_em">

              <div>
              <q-splitter
                v-model="splitterModel"
              >

                <template v-slot:before>
                  <q-tabs
                    v-model="tabProfessionalSub"
                    vertical
                    class="text-primary"
                  >
                    
                    <q-tab name="worksheet" icon="healing" label="Worksheet" />
                    <q-tab name="cpt" icon="medication_liquid" label="Procedures" />
                  </q-tabs>
                </template>

                <template v-slot:after>
                  <q-tab-panels
                    v-model="tabProfessionalSub"
                    animated
                    swipeable
                    vertical
                    transition-prev="jump-up"
                    transition-next="jump-up"
                  >
                    <q-tab-panel name="worksheet">
                      <ClinicWorksheetProfessional />
                    </q-tab-panel>

                    <q-tab-panel name="cpt">
                      <ProcedureCodesProfessional />
                    </q-tab-panel>

                  </q-tab-panels>
                </template>

              </q-splitter>
            </div>

            </q-tab-panel>
  
            <q-tab-panel name="facility_em">
              <div>
              <q-splitter
                v-model="splitterModel"
              >

                <template v-slot:before>
                  <q-tabs
                    v-model="tabFacilitySub"
                    vertical
                    class="text-primary"
                  >
                    
                    <q-tab name="worksheet" icon="healing" label="Worksheet" />
                    <q-tab name="cpt" icon="medication_liquid" label="Procedures" />
                  </q-tabs>
                </template>

                <template v-slot:after>
                  <q-tab-panels
                    v-model="tabFacilitySub"
                    animated
                    swipeable
                    vertical
                    transition-prev="jump-up"
                    transition-next="jump-up"
                  >
                    <q-tab-panel name="worksheet">
                      <ClinicWorksheetFacility />
                    </q-tab-panel>

                    <q-tab-panel name="cpt">
                      <ProcedureCodesFacility />
                    </q-tab-panel>

                  </q-tab-panels>
                </template>

              </q-splitter>
            </div>
            </q-tab-panel>
  
            <q-tab-panel name="observation_stays">
                In pretium bibendum ligula, eu tincidunt ligula facilisis nec. Proin dignissim eleifend dolor, nec fringilla odio aliquet molestie. Nunc vestibulum, est a porttitor fermentum, purus sapien maximus libero, eu imperdiet nunc lectus quis purus. Donec et metus magna. Cras posuere dapibus turpis suscipit vestibulum. In aliquam tempus nisi eu fringilla. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam cursus dolor et ullamcorper porta. 
            </q-tab-panel>

            <q-tab-panel name="infusions_injections">
              <div class="col-12 col-md-auto">
                <InfusionsInjections />
              </div>
            </q-tab-panel>

            <q-tab-panel name="x_ray">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer molestie blandit nibh ac pharetra. In hac habitasse platea dictumst. Etiam dictum est lectus, ut dapibus nisl scelerisque ut. Suspendisse maximus risus ante, eu rutrum diam sagittis eget. Aenean tempor volutpat lobortis. Fusce malesuada interdum justo et fermentum. Morbi nisl lectus, consectetur vel tempus eu, eleifend a neque. Phasellus ut felis urna. Phasellus egestas justo eget rutrum dapibus. Maecenas congue nisi vitae eros bibendum scelerisque. 
            </q-tab-panel>

            <q-tab-panel name="ultrasound">
                Suspendisse fringilla vestibulum vulputate. Quisque at lobortis dui. Ut at rutrum odio, quis porttitor mauris. Donec cursus eget dolor sit amet posuere. Fusce fringilla augue sit amet nisi dignissim rutrum. Sed non nisi in erat facilisis sagittis vel sit amet nisl. Donec faucibus enim in consectetur tincidunt. 
            </q-tab-panel>

            <q-tab-panel name="laceration_repair">
                In pretium bibendum ligula, eu tincidunt ligula facilisis nec. Proin dignissim eleifend dolor, nec fringilla odio aliquet molestie. Nunc vestibulum, est a porttitor fermentum, purus sapien maximus libero, eu imperdiet nunc lectus quis purus. Donec et metus magna. Cras posuere dapibus turpis suscipit vestibulum. In aliquam tempus nisi eu fringilla. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam cursus dolor et ullamcorper porta. 
            </q-tab-panel>

            <q-tab-panel name="diagnosis_codes">
              <div class="col-12 col-md-auto">
                <DiagnosisCodes />
              </div>
            </q-tab-panel>

            <q-tab-panel name="mips">
                Nam mattis rutrum leo, et tincidunt lacus feugiat sit amet. Praesent orci ante, pellentesque semper libero et, commodo sodales magna. Nullam venenatis sapien massa, et volutpat eros efficitur eget. Donec vel orci in lectus cursus hendrerit vel at ex. Quisque rutrum augue eu risus maximus condimentum. In tincidunt blandit orci, ut pellentesque diam. Nulla facilisi. Phasellus imperdiet sagittis leo. Nunc interdum, neque ut suscipit convallis, ligula diam varius velit, eu condimentum diam leo in tortor. Nunc accumsan ut leo quis auctor. Sed odio ante, feugiat eget egestas ac, ultricies id arcu. 
                <ProceduresOther />
            </q-tab-panel>

            <q-tab-panel name="hcspcs">
                Nam mattis rutrum leo, et tincidunt lacus feugiat sit amet. Praesent orci ante, pellentesque semper libero et, commodo sodales magna. Nullam venenatis sapien massa, et volutpat eros efficitur eget. Donec vel orci in lectus cursus hendrerit vel at ex. Quisque rutrum augue eu risus maximus condimentum. In tincidunt blandit orci, ut pellentesque diam. Nulla facilisi. Phasellus imperdiet sagittis leo. Nunc interdum, neque ut suscipit convallis, ligula diam varius velit, eu condimentum diam leo in tortor. Nunc accumsan ut leo quis auctor. Sed odio ante, feugiat eget egestas ac, ultricies id arcu. 
            </q-tab-panel>

          </q-tab-panels>
        </q-card>
      </div>
    </div>
</template>

<style lang="sass">
.services
  margin: 2px
  padding: 2px
</style>