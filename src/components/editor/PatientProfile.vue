<script setup lang="ts">
import { ref } from 'vue'

defineProps<{
  firstName: string,
  lastName: string,
  dob: string,
  age: number,
  dos: string,
  fin: string,
  mrn: string,
  payer: string,
  disposition: string,
  coder: string,
  status: string
}>()

const expanded = ref(false)
</script>

<template>
  <div class="q-pa-md profile">
    <q-card flat bordered>
      <q-card-actions>
        <q-space />
        <q-btn
          color="grey"
          round
          flat
          dense
          :icon="expanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
          @click="expanded = !expanded"
        />
      </q-card-actions>
      <q-slide-transition>
        <div v-show="expanded" style="min-width: 250px">
          <q-card-section horizontal>
            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label>Patient Name</q-item-label>
                  <q-item-label caption>{{ firstName }} {{ lastName }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Date of Birth</q-item-label>
                  <q-item-label caption>{{ dob }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Age</q-item-label>
                  <q-item-label caption>{{ age }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Date of Service</q-item-label>
                  <q-item-label caption>{{ dos }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>FIN #</q-item-label>
                  <q-item-label caption>{{ fin }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>MRN</q-item-label>
                  <q-item-label caption>{{ mrn }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Payer</q-item-label>
                  <q-item-label caption>{{ payer }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Disposition</q-item-label>
                  <q-item-label caption>{{ disposition }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Render Provider</q-item-label>
                  <q-item-label caption></q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Billing Provider</q-item-label>
                  <q-item-label caption></q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Coder</q-item-label>
                  <q-item-label caption>{{ coder }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-separator spaced inset />
              <q-item>
                <q-item-section>
                  <q-item-label>Status</q-item-label>
                  <q-item-label caption>{{ status }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </div>
      </q-slide-transition>
    </q-card>
  </div>
</template>

<style lang="sass">
.profile
  min-width: 50px
  margin: 2px
  padding: 2px
</style>
