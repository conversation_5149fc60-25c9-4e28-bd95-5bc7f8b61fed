<template>
  <div class="q-pa-md">
    <q-table
      class="my-sticky-header-table"
      flat bordered
      title="Facilities"
      :rows="facilityDataList"
      :columns="columns"
      row-key="facility"
      :visible-columns="columnsVisible"
    >

    <template v-slot:top>
      <q-space />
      <q-select
        v-model="columnsVisible"
        multiple
        outlined
        dense
        options-dense
        :display-value="$q.lang.table.columns"
        emit-value
        map-options
        :options="columns"
        option-value="name"
        options-cover
        style="min-width: 150px"
      />
    </template>

      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th auto-width />
          <q-th
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <q-btn size="sm" color="primary" square dense @click="props.expand = !props.expand" :icon="props.expand ? 'remove' : 'add'" />
          </q-td>
          <q-td
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.value }}
          </q-td>
        </q-tr>
        <q-tr v-show="props.expand" :props="props">
          <q-td colspan="100%">
            <div class="text-left">
                <q-card class="my-card" flat bordered>
                  <q-card-section horizontal>
                    <q-card-section class="q-pt-xs">
                      <div class="text-h5 q-mt-sm q-mb-xs">{{ props.row.facility_display }}</div>
                      <div class="text-caption text-grey">
                        <div> Client: {{ props.row.client_display }} </div>
                        <div> With Coding: {{ props.row.with_coding }} </div>
                        <div> Incomplete: {{ props.row.incomplete }} </div>
                        <div> Reviewed: {{ props.row.reviewed }} </div>
                      </div>
                    </q-card-section>
                  </q-card-section>

                  <q-separator />

                  <q-card-actions>
                    <q-btn size="md" square color="primary" label="View" :href="'charts/' + props.row.facility" />
                  </q-card-actions>
                </q-card>
            </div>
          </q-td>
        </q-tr>
      </template>

    </q-table>
  </div>
</template>
  
<script lang="ts">
  import { ref } from 'vue'
  import axios from "axios";
  import { QTable } from "quasar";
  import VueApexCharts from 'vue3-apexcharts'
  import { fetchAuthSession } from 'aws-amplify/auth';

  const columns = [
    {name: 'facility', label: 'Facility', field: 'facility_display', required: true, sortable: true},
    {name: 'new', label: 'New', field: 'new', sortable: true},
    {name: 'updated', label: 'Updated', field: 'updated', sortable: true},
    {name: 'in_progress', label: 'In Progress', field: 'in_progress', sortable: true},
    {name: 'qa', label: 'QA', field: 'qa', sortable: true},
    {name: 'deficient', label: 'Deficient', field: 'deficient', sortable: true},
    {name: 'complete', label: 'Complete', field: 'complete', sortable: true},
    {name: 'export', label: 'Export', field: 'export', sortable: true}
  ]

  export default {
    name: "facilities",
    data() {
      return {
        facilityDataList: []
      };
    },

    methods: {
      async getFacilitiesData() {

        const session = await fetchAuthSession();
        let config = {
        headers: {
          authorization: session.tokens?.idToken?.toString() || localStorage.getItem('jwt'),
          }
        }

        axios
          .get("https://vzw8xog2f7.execute-api.us-east-1.amazonaws.com/prod/encountersinit/stats", config)
          .then(response => (this.facilityDataList = response.data));
      }
    },

    mounted: function () {
      this.getFacilitiesData()
    },

    setup() {
      return {
        columns,
        columnsVisible: ref([
                            'facility',
                            'new',
                            'updated',
                            'in_progress',
                            'qa',
                            'deficient',
                            'complete',
                            'export'
                          ])
      }
    }
  };
</script>