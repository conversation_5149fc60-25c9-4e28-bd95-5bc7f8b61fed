<script setup lang="ts">
    import PatientProfile from './editor/PatientProfile.vue'
    import ServicesPanel from './editor/ServicesPanel.vue'
</script>

<template>
  <div class="q-pa-md encounter">
    <div class="row justify-center">
      <div class="col-12 col-md-auto">
        <PatientProfile :firstName= "chartData.demographics.firstName" 
                        :lastName= "chartData.demographics.lastName"
                        :dob= "chartData.demographics.dob"
                        :age= "chartData.demographics.age"
                        :dos= "chartData.dos"
                        :fin= "chartData.ext_id"
                        :mrn= "chartData.demographics.mrn"
                        :payer= "chartData.demographics.payer"
                        :disposition= "chartData.discharge_disposition"
                        :coder= "chartData.lastModifiedUser"
                        :status= "chartData.status"/>
      </div>
      <div class="col-12 col-md-auto">
        <ServicesPanel/>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import axios from "axios";
  import { Auth } from 'aws-amplify';

  export default {
    name: "chart",
    data() {
      return {
        chartData: {
            dos: '',
            ext_id: '',
            discharge_disposition: '',
            lastModifiedUser: '',
            status: '',
            demographics: {
              firstName: '',
              lastName: '',
              dob: '',
              age: 0,
              mrn: '',
              payer: ''
            }
        }
      };
    },

    methods: {
      getChartData() {
            let config = {
                headers: {
                    authorization: localStorage.getItem('jwt'),
                }
            }

            axios
                .get("https://vzw8xog2f7.execute-api.us-east-1.amazonaws.com/v2/encounters/TALL_AUDIT/01F87ZW2M7K1AXBBDKWKNN3RB0", config)
                .then(response => (this.chartData = response.data));
      }
    },

    mounted: function () {
      this.getChartData()
    },
  };
</script>

<style lang="sass">
.encounter
  min-width: 1480px
  width: 100%
  .row + .row
    margin-top: 1rem
</style>