<script setup lang="ts">
  import { fetchAuthSession } from 'aws-amplify/auth'
  import 'quasar/src/css/index.sass'
  import "@aws-amplify/ui-vue/styles.css"
  defineProps<{msg: string}>()
  fetchAuthSession().then(session => {
    if (session.tokens?.idToken) {
      localStorage.setItem('jwt', session.tokens.idToken.toString())
    }
  })
  console.log(fetchAuthSession)
</script>

<template>
  <div class="greetings">
    <div class="q-pa-md brand-box">
      <div class="row">
        <div class="col-12 col-md-auto">
          <img src="@/assets/logo.svg" style="height: 70px; max-width: 100px"><img>
        </div>
        <div class="col-12 col-md-auto">
          <h1 class="green">{{ msg }}</h1>
          AbacusOne
        </div>
      </div>

      <div class="row">
        <div class="col-12 col-md-auto">
          Welcome to Expericoder version 2.0 by AbacusOne. Expericoder is created with 
          <a href="https://vuejs.org/" target="_blank" rel="noopener">Vue 3</a> 
          and powered by 
          <a href="https://aws.amazon.com/" target="_blank" rel="noopener">Amazon AWS</a>.
        </div>
      </div>

      <div class="row">
        <div class="col-12 col-md-auto">
          <authenticator :hide-sign-up="true">
            <template v-slot="{ user, signOut }">
              <div>You are currently signed in as {{ user.username }}. <a @click="signOut">Sign out.</a></div>
              <!-- <button @click="signOut">Sign Out</button> -->
            </template>
          </authenticator>
        </div>
      </div>
    </div>
    <br/>
  </div>
</template>

<style scoped>
h1 {
  font-weight: 500;
  font-size: 2.6rem;
  line-height: 3rem;
  text-align: left;
}

h3 {
  font-size: 1.2rem;
}
.greetings {
  margin: 50px;
}

.greetings h1,
.greetings h3 {
  text-align: left;
}

@media (min-width: 1024px) {
  .greetings h1,
  .greetings h3 {
    text-align: left;
  }
}
</style>

<style lang="sass">
.brand-box
  .row
    padding: 0px
  .row > div
    padding: 0px
  .row + .row
    margin-top: 1rem
</style>