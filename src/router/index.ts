import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

// New Views
import FacilitiesView from '../views/FacilitiesView.vue'
import ChartsView from '../views/ChartsView.vue'
import EditorView from '../views/EditorView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue')
    },
    {
      path: '/facilities',
      name: 'facilities',
      component: FacilitiesView
    },
    {
      path: '/charts/:facility',
      name: 'charts',
      component: ChartsView
    },
    {
      path: '/editor/:facility/:chart',
      name: 'editor',
      component: <PERSON><PERSON><PERSON><PERSON>
    }
  ]
})

export default router
