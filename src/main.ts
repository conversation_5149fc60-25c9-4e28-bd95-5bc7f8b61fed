import { createApp } from 'vue'
import { create<PERSON><PERSON> } from 'pinia'
import { Quasar, Notify, Dark } from 'quasar' // Quasar must be installed: https://quasar.dev/start/vite-plugin#introduction

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css'
import '@quasar/extras/themify/themify.css'

// Import Quasar css
import 'quasar/src/css/index.sass'

import App from './App.vue'
import router from './router'
import VueApexCharts from "vue3-apexcharts";

import './assets/main.css'

import { Authenticator } from '@aws-amplify/ui-vue' // Amplify must be installed: https://ui.docs.amplify.aws/vue/getting-started/introduction
import '@aws-amplify/ui-vue/styles.css';

// Import custom components
import DiagnosisCodes from "./components/editor/panels/DiagnosisCodes.vue"
import ProcedureCodesFacility from "./components/editor/panels/ProcedureCodesFacility.vue"

import ProcedureCodesProfessional from "./components/editor/panels/ProcedureCodesProfessional.vue"
import ProceduresOther from "./components/editor/panels/ProceduresOther.vue"

import ClinicWorksheetProfessional from "./components/editor/panels/ClinicWorksheetProfessional.vue"
import ClinicWorksheetFacility from "./components/editor/panels/ClinicWorksheetFacility.vue"

import InfusionsInjections from "./components/editor/panels/InfusionsInjections.vue"

const app = createApp(App)

app.component('DiagnosisCodes', DiagnosisCodes);
app.component('ProceduresOther', ProceduresOther);

app.component('ProcedureCodesFacility', ProcedureCodesFacility);
app.component('ProcedureCodesProfessional', ProcedureCodesProfessional);

app.component('ClinicWorksheetProfessional', ClinicWorksheetProfessional);
app.component('ClinicWorksheetFacility', ClinicWorksheetFacility);

app.component('InfusionsInjections', InfusionsInjections);

app.use(createPinia())
app.use(router)
app.component('apexchart', VueApexCharts) // Install ApexCharts: https://apexcharts.com/docs/vue-charts/
app.component('Authenticator', Authenticator)

app.use(Quasar, {
    plugins: {Notify, Dark},
    config: {notify: {position: 'top'}, dark: false }
  })

app.mount('#app')