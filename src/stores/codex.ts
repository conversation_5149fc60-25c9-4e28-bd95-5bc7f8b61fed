// Codex/Apotheca
import { defineStore } from 'pinia'
interface ProcedureCode {
    code: number;
    mnemonic: number;
    description: string;
    hcpcsCode: string | null;
    revenueCode: number | null;
    group: string;
  }
  export interface IniRule {
    input: {
      type: string;
      sites: string[] | null;
      duration?: { min: number; max: number };
      drugCount?: { min: number; max: number };
    };
    output: {
      procedures: Array<{
        procedure: number;
        quantity?: number | { duration: number; factor: number };
      }>;
      charges: boolean;
    };
    required: {
      department: boolean;
      date: boolean;
      drugMinimum: number;
      timeStart: boolean;
      timeStartAlternative: boolean;
      timeStop: boolean;
      timeStopNone: { timeStop: null; value: boolean };
      site: boolean;
      siteUnrecorded: { site: null; value: boolean };
      dayNext: boolean;
    };
    duplicateAllowed: boolean;
    priority: number;
    notes: string;
  }
export const useCodexStore = defineStore('codex', {
    state: () => {
        return { procedureCodes: [
                  {
                    code: 96368,
                    mnemonic: 2600000025,
                    description: 'Drng abc mouth comp/pc',
                    hcpcsCode: null,
                    revenueCode: null,
                    group: 'Injection and Infusion'
                  },
                  {
                    code: 96368,
                    mnemonic: 2600000025,
                    description: 'Drng abc mouth comp/pc',
                    hcpcsCode: null,
                    revenueCode: null,
                    group: 'Injection and Infusion'
                  },
                  {
                    code: 96368,
                    mnemonic: 2600000025,
                    description: 'Drng abc mouth comp/pc',
                    hcpcsCode: null,
                    revenueCode: null,
                    group: 'Injection and Infusion'
                  },
                  {
                    code: 96368,
                    mnemonic: 2600000025,
                    description: 'Drng abc mouth comp/pc',
                    hcpcsCode: null,
                    revenueCode: null,
                    group: 'Injection and Infusion'
                  },
                  {
                    code: 96368,
                    mnemonic: 2600000025,
                    description: 'Drng abc mouth comp/pc',
                    hcpcsCode: null,
                    revenueCode: null,
                    group: 'Injection and Infusion'
                  }
                ],
                iniSites: [
                  {
                    value: "Arterial Access Device",
                    description: "A device used to access an artery for infusion or blood sampling."
                  },
                  {
                    value: "Antecubital Right",
                    description: "The front of the right elbow, a common site for IV insertion and blood draws."
                  },
                  {
                    value: "Antecubital Left",
                    description: "The front of the left elbow, frequently used for IV access and venipuncture."
                  },
                  {
                    value: "Arterial line Right",
                    description: "A catheter inserted into the right artery for continuous blood pressure monitoring or sampling."
                  },
                  {
                    value: "Arterial line left",
                    description: "A catheter inserted into the left artery for monitoring or blood sampling."
                  },
                  {
                    value: "Basilic Vein Right",
                    description: "The basilic vein on the right arm, commonly used for peripheral IV insertion."
                  },
                  {
                    value: "Basilic Vein Left",
                    description: "The basilic vein on the left arm, often selected for IV access."
                  },
                  {
                    value: "Brachial Right",
                    description: "The right brachial region (vein/artery) used for IV or arterial access."
                  },
                  {
                    value: "Brachial Left",
                    description: "The left brachial region, suitable for IV insertion or arterial monitoring."
                  },
                  {
                    value: "Central Multilumen Blue",
                    description: "A central venous catheter with multiple lumens, typically used for complex infusions."
                  },
                  {
                    value: "Foot Right",
                    description: "The right foot, occasionally used for peripheral IV insertion when other sites are not available."
                  },
                  {
                    value: "Foot Left",
                    description: "The left foot, an alternative site for IV insertion if needed."
                  },
                  {
                    value: "Hand Right",
                    description: "The right hand, used for IV access or injections in peripheral settings."
                  },
                  {
                    value: "Hand Left",
                    description: "The left hand, utilized for peripheral IV access or injection administration."
                  },
                  {
                    value: "Implantable/Portable Device",
                    description: "An infusion pump or device that is implantable or portable for long-term or ambulatory therapy."
                  },
                  {
                    value: "Scalp",
                    description: "The skin covering the skull; in neonates, the scalp is sometimes used for IV access."
                  },
                  {
                    value: "Wrist Right",
                    description: "The right wrist area, occasionally used for IV or injection administration."
                  },
                  {
                    value: "Wrist Left",
                    description: "The left wrist area, sometimes selected for IV access or injections."
                  },
                  {
                    value: "IO",
                    description: "Intraosseous access, which involves infusing directly into the bone marrow, often used in emergencies."
                  }
                ],
                /**
                 * Injection and Infusion Business Rules
                 * Important points:
                 * 
                 * 1. Procedure selection is done per site.
                 * 2. There must be an initial code per site.
                 * 3. There can only be one itial code and that depends on the hierarchy/priority.
                 * 4. Determining the sequence of drug administration using time frames stated in the rules, overlappping cannot happen in most instances.
                 */
                iniRules: [
                  {
                    input: {
                      type: "Theraputic Hydration",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 1, max: 30 }
                    },
                    output: {
                      procedure: null,
                      charges: false
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: false,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 0,
                    notes: "Hydration under 31 minutes is not billable. Hydration is lowest; if an IV infusion or IV push occurs concurrently at the same site, hydration is omitted."
                  },
                  {
                    input: {
                      type: "Theraputic Hydration",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 31, max: 90 }
                    },
                    output: {
                      procedures: [{ procedure: 96360, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: false,
                      dayNext: false
                    },
                    initial: true,
                    addon: false,
                    duplicateAllowed: false,
                    priority: 0,
                    notes: "One 96360 per vascular site per encounter. Hydration is omitted if a higher-priority procedure occurs at the same site concurrently."
                  },
                  {
                    input: {
                      type: "Theraputic Hydration",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 91, max: 510 }
                    },
                    output: {
                      procedures: [{ procedure: 96360, quantity: 1 },{ procedure: 96361, quantity: { duration: 90, factor: 60 } }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: false,
                      dayNext: false
                    },
                    initial: false,
                    addon: true,
                    duplicateAllowed: false,
                    priority: 0,
                    notes: "Initial code 96360. Extended hydration adds 96361 for each additional unit. Hydration remains lowest; superseded if another procedure occurs concurrently at the same site."
                  },
                  {
                    input: {
                      type: "Medication IV Push",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 1, max: 15} 
                    },
                    output: {
                      procedures: [{ procedure: 96374, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 1,
                    notes: "96374 is the initial IV push of a medication, allowed once per encounter per vascular site unless for a different drug."
                  },
                  {
                    input: {
                      type: "Medication IV Push",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 16, max: 30 }
                    },
                    output: {
                      procedures: [{ procedure: 96375, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 1,
                    notes: "96375 is used for a sequential push of a new medication, following an initial IV push."
                  },
                  {
                    input: {
                      type: "Medication IV Push",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 31, max: 1440 }
                    },
                    output: {
                      procedures: [{ procedure: 96374, quantity: 1 },{ procedure: 96376, quantity: { duration: 90, factor: 60 } }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 1,
                    notes: "96376 is reported for repeat IV push of the same drug given at least 30 minutes apart."
                  },
                  {
                    input: {
                      type: "Medication IV Infusion",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 1, max: 15 }
                    },
                    output: {
                      procedure: null,
                      charges: false
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 1,
                    notes: "Medication IV Infusion under 16 minutes is not billable. Minimum infusion time must be at least 16 minutes."
                  },
                  {
                    input: {
                      type: "Medication IV Infusion",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 16,   max: 90 },
                      excludes: ['Chemotherapy', 'Medication']
                    },
                    output: {
                      procedures: [{ procedure: 96365, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 2,
                    notes: "Medication IV Infusion initial code 96365 is billed for infusions lasting 16–90 minutes."
                  },
                  {
                    input: {
                      type: "Medication IV Infusion",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 91, max: 510 }
                    },
                    output: {
                      procedures: [{ procedure: 96365, quantity: 1 },{ procedure: 96366, quantity: { duration: 90, factor: 60 } }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 2,
                    notes: "Medication IV Infusion extended: 96366 is billed for each additional hour beyond 90 minutes."
                  },
                  // 96367 – Intravenous infusion, for therapy, prophylaxis, or diagnosis; additional sequential infusion of a new substance; up to 1 hour. (Depends on 96365)
                  // 96368 – Intravenous infusion, for therapy, prophylaxis, or diagnosis; concurrent infusion (report only once per encounter).
                  // 96369 – Subcutaneous infusion for therapy or prophylaxis (specify substance or drug); initial, up to 1 hour, including pump set-up and establishment of subcutaneous infusion site(s).
                  // 96370 – Subcutaneous infusion for therapy or prophylaxis; each additional hour (list separately in addition to code for primary procedure).
                  // 96371 – Subcutaneous infusion for therapy or prophylaxis; additional pump set-up with establishment of new subcutaneous infusion site(s) (list separately).
                  // 96372 – Therapeutic, prophylactic, or diagnostic injection (specify substance or drug); subcutaneous or intramuscular.
                  // 96373 – Therapeutic, prophylactic, or diagnostic infusion (specify substance or drug); subcutaneous, e.g., via pump
                  // 96374 – Therapeutic, prophylactic, or diagnostic injection (specify substance or drug); intravenous push; single or initial substance/drug.
                  // 96375 – Therapeutic, prophylactic, or diagnostic injection (specify substance or drug); each additional sequential IV push or a new substance/drug.
                  // 96376 – Therapeutic, prophylactic, or diagnostic injection; each additional sequential IV push of the same substance/drug; facility only (report only if > 30 minutes after a previous push).
                  // 96379 – Unlisted therapeutic, prophylactic, or diagnostic intravenous or intra-arterial injection or infusion service.
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 16, max: 90 }
                    },
                    output: {
                      procedures: [{ procedure: 96413, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 3,
                    notes: "96413 is the initial chemotherapy or other highly complex drug infusion (more than 15 minutes), per visit, per vascular site."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 16, max: 90 }
                    },
                    output: {
                      procedures: [{ procedure: 96417, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 3,
                    notes: "96417 is used for each additional sequential infusion of a new chemotherapy drug after 96413."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 91, max: 600 }
                    },
                    output: {
                      procedures: [{ procedure: 96417, quantity: 1 },{ procedure: 96415, quantity: { duration: 90, factor: 60 } }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: true,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 3,
                    notes: "96415 is for each additional hour of chemotherapy infusion following 96413 or 96417. Use for prolonged duration."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 1, max: 15 }
                    },
                    output: {
                      procedures: [{ procedure: 96409, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 3,
                    notes: "96409 is used for initial IV push administration of chemotherapy drug (15 minutes or less)."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 1, max: 15 }
                    },
                    output: {
                      procedures: [{ procedure: 96411, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStartAlternative: false,
                      timeStop: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 3,
                    notes: "96411 is reported for each additional sequential IV push of a different chemotherapy drug after 96409."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: ["Arterial line Right", "Arterial line Left"],
                      drugCount: { min: 1, max: Infinity }, //Arterial Infusion
                      duration: { min: 1, max: 15 }
                    },
                    output: {
                      procedures: [{ procedure: 96420, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStop: true,
                      timeStartAlternative: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 4,
                    notes: "96420 is for chemotherapy administration via intra-arterial push technique (≤15 min)."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: ["Arterial line Right", "Arterial line Left"], //Arterial Infusion Extended
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 16, max: 600 }
                    },
                    output: {
                      procedures: [{ procedure: 96422, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStop: true,
                      timeStartAlternative: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: false,
                    priority: 4,
                    notes: "96422 is for initial chemotherapy administration via intra-arterial infusion (16-90 mins)."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 91, max: 600 }
                    },
                    output: {
                      procedures: [{ procedure: 96423, quantity: 1 },{ procedure: 96423, quantity: { duration: 90, factor: 60 } }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStop: true,
                      timeStartAlternative: false,
                      site: true,
                      dayNext: false
                    },
                    duplicateAllowed: true,
                    priority: 4,
                    notes: "96423 is for each additional hour of intra-arterial chemotherapy infusion after 96422."
                  },
                  {
                    input: {
                      type: "Chemotherapy",
                      sites: null,   //Implanted Device Infusion
                      drugCount: { min: 1, max: Infinity },
                      duration: { min: 480, max: 1440 }
                    },
                    output: {
                      procedures: [{ procedure: 96416, quantity: 1 }],
                      charges: true
                    },
                    required: {
                      department: true,
                      date: true,
                      drugMinimum: 1,
                      timeStart: true,
                      timeStop: true,
                      timeStartAlternative: false,
                      site: true,
                      dayNext: true
                    },
                    duplicateAllowed: false,
                    priority: 4,
                    notes: "96416 is for chemotherapy infusion through an implanted pump or portable device lasting at least 8 hours."
                  }
                ]                
              }
      },
      actions: {
        //Accept filter value and return filtered proecdure codes list.
        getProcedureCodes(filter: string): ProcedureCode[] {
            return this.procedureCodes.filter(
                code => code.description.toLowerCase() === filter.toLowerCase()
            );
        }
      },
})