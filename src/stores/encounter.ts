import { defineStore } from 'pinia'
import { useCodexStore } from '@/stores/codex';
import type { IniRule } from '@/stores/codex';

interface InfusionInjection {
  index: number;
  selected: boolean;
  date: string;
  type: string;
  site: string;
  drugs: string[];
  start: string;
  stop: string;
  duration: number;
  durationActual: number;
  nextDay: boolean;
  department: string;
  procedures: Array<{
    procedure: number;
    quantity: number | null;
    modifiers?: string[];
  }> | null;
  initialCode: boolean;
  charges: boolean;
}

export const useEncounterStore = defineStore('encounter', {
    state: () => {
        return { procedureCodesFacility: [
                  {
                    selected: false,
                    index: 1,
                    type: 'CPT',
                    mods: ['22'],
                    code: '40801',
                    description: 'Drng abc mouth comp/pc',
                    q: 5,
                    bp: '<PERSON>',
                    sp: '<PERSON> NP',
                    date: '2023/05/11',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 2,
                    type: 'CPT',
                    mods: ['23', '26'],
                    code: '20612',
                    description: 'Asp and/or inj ganglion cys',
                    q: 1,
                    bp: '<PERSON>',
                    sp: '<PERSON>',
                    date: '2023/05/04',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 3,
                    type: 'CPT',
                    mods: ['32'],
                    code: '38300',
                    description: 'Drng lymph node ab sim/pc',
                    q: 3,
                    bp: 'Ernest Sorini MD',
                    sp: 'Bertsche Debra PA',
                    date: '2023/05/05',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 4,
                    type: 'CPT',
                    mods: ['32', '33'],
                    code: '40654',
                    description: 'Rep lip full thickness < 1/2 ve',
                    q: 2,
                    bp: 'Harry Aretakis MD',
                    sp: 'Jennifer Driggers PA',
                    date: '2023/05/04',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 5,
                    type: 'CPT',
                    mods: ['22', '23'],
                    code: '40801',
                    description: 'Drng abc mouth comp/pc',
                    q: 4,
                    bp: 'John Bauer MD',
                    sp: 'Robilyn Collins NP',
                    date: '2023/05/07',
                    diagnosis: null
                  }
                ],
                procedureCodesProfessional: [
                  {
                    selected: false,
                    index: 1,
                    type: 'CPT',
                    mods: ['22'],
                    code: '40801',
                    description: 'Drng abc mouth comp/pc',
                    q: 5,
                    bp: 'Ernest Sorini MD',
                    sp: 'Susan Secrete NP',
                    date: '2023/05/11',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 2,
                    type: 'CPT',
                    mods: ['23', '26'],
                    code: '20612',
                    description: 'Asp and/or inj ganglion cys',
                    q: 1,
                    bp: 'Walter Kaniefski MD',
                    sp: 'Jennifer Driggers PA',
                    date: '2023/05/04',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 3,
                    type: 'CPT',
                    mods: ['32'],
                    code: '38300',
                    description: 'Drng lymph node ab sim/pc',
                    q: 3,
                    bp: 'Ernest Sorini MD',
                    sp: 'Bertsche Debra PA',
                    date: '2023/05/05',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 4,
                    type: 'CPT',
                    mods: ['32', '33'],
                    code: '40654',
                    description: 'Rep lip full thickness < 1/2 ve',
                    q: 2,
                    bp: 'Harry Aretakis MD',
                    sp: 'Jennifer Driggers PA',
                    date: '2023/05/04',
                    diagnosis: null
                  },
                  {
                    selected: false,
                    index: 5,
                    type: 'CPT',
                    mods: ['22', '23'],
                    code: '40801',
                    description: 'Drng abc mouth comp/pc',
                    q: 4,
                    bp: 'John Bauer MD',
                    sp: 'Robilyn Collins NP',
                    date: '2023/05/07',
                    diagnosis: null
                  }
                ],
                diagnosisCodes: [
                {
                  index: 0,
                  priority: 0,
                  selected: false,
                  type: 'DX',
                  code: 'J12.82',
                  description: 'Pneumonia due to coronavirus disease 2019',
                  cpt: [{ code: '100801', category: "professional"},{ code: '40801', category: "professional"}, { code: '38300', category: "facility"}]
                },
                {
                  index: 1,
                  priority: 1,
                  selected: false,
                  type: 'DX',
                  code: 'E55.9',
                  description: 'Vitamin D deficiency, unspecified',
                  cpt: [{ code: '100801', category: "professional"}, { code: '38300', category: "facility"}]
                },
                {
                  index: 2,
                  priority: 2,
                  selected: false,
                  type: 'DX',
                  code: 'R29.709',
                  description: 'NIHSS score 9',
                  cpt: [{ code: '12001', category: "facility"}]
                },
                {
                  index: 3,
                  priority: 3,
                  selected: false,
                  type: 'DX',
                  code: 'S22.079S',
                  description: 'Unspecified fracture of T9-T10 vertebra, sequela',
                  cpt: [{ code: '40654', category: "professional"}, { code: '10081', category: "facility"}]
                },
                {
                  index: 4,
                  priority: 4,
                  selected: false,
                  type: 'DX',
                  code: 'S23.150S',
                  description: 'Subluxation of T8/T9 thoracic vertebra, sequela',
                  cpt: [{ code: '10081', category: "professional"}, { code: '40654', category: "facility"}]
                },
                {
                  index: 5,
                  priority: 5,
                  selected: false,
                  type: 'DX',
                  code: 'M26.211',
                  description: "Malocclusion, Angle's class I",
                  cpt: [{ code: '10081', category: "professional"}]
                },
                {
                  index: 6,
                  priority: 6,
                  selected: false,
                  type: 'DX',
                  code: 'M35.81',
                  description: 'Multisystem inflammatory syndrome',
                  cpt: [{ code: '10081', category: "encounter"}]
                }
          ],
          InfusionsInjections: [
            {
              index: 0,
              selected: false,
              date: '2024-12-01',
              type: 'Theraputic Hydration',
              site: 'Antecubital Right',
              drugs: ['Normal Saline'],
              start:'09:00',
              stop: '17:00',
              duration: 240,
              durationActual: 480,
              nextDay: false,
              department: 'FGHED EMERGENCY SERVICES - 2600',
              procedures: [{ procedure: 96360, quantity: 1, modifiers: ['22']}],
              initialCode: false,
              charges: false
            },
            {
              index: 1,
              selected: false,
              date: '2024-12-01',
              type: 'Medication IV Push',
              site: 'Antecubital Right',
              drugs: ['Keotorolac','Morphine'],
              start: '10:00',
              stop: '12:00',
              duration: 120,
              durationActual: 120,
              nextDay: false,
              department: 'FGHED EMERGENCY SERVICES - 2600',
              procedures: [{ procedure: 96374, quantity: 1, modifiers: null }, { procedure: 96376, quantity: 6, modifiers: null}],
              initialCode: true,
              charges: false
            },
            {
              index: 2,
              selected: false,
              date: '2024-12-01',
              type: 'Medication IV Push',
              site: 'Antecubital Right',
              drugs: ['Magnesium Sulfate','Meropenem'],
              start: '13:00',
              stop: '15:00',
              duration: 120,
              durationActual: 120,
              nextDay: false,
              department: 'FGH-6T-ONCOLOGY AND NEURO - 1010',
              procedures: [{ procedure: 96374, quantity: 1, modifiers: null }, { procedure: 96376, quantity: 2, modifiers: ['57', '63']}],
              initialCode: false,
              charges: false
            }
      ] as InfusionInjection[]}
      },
      actions: {
        /**
         * Parses time string into milliseconds since epoch
         * @param time - Time in HH:MM format (24-hour clock)
         * @param tomorrow - Indicates if time is on next day
         * @returns Milliseconds since epoch
         * @note Used for comparing times across days
         * @complexity O(1)
         * @sideeffect None
         * @example
         * // Returns 3600000 for 01:00 on same day
         * temporisCalculum("01:00", false);
         */
        temporisCalculum(time: string, tomorrow: boolean): number {
          return new Date(`1970-01-${tomorrow ? '02' : '01'}T${time}:00`).getTime();
        },

        /**
         * Calculates infusion/injection duration for hydration administration while accounting for overlapping medication administration at the same site
         * @param start - Start time in HH:MM format (24-hour clock)
         * @param stop - End time in HH:MM format (24-hour clock)
         * @param tomorrow - Indicates if stop time crosses midnight
         * @returns Duration in minutes
         * @note Very basic duration calculation
         * @complexity O(1)
         * @sideeffect Logs calculation result to console
         * @example
         * // Returns 120 for 10:00-12:00 session
         * iniCalculateDurationSimple("10:00", "12:00", false);
         */
        iniCalculateDurationSimple(start: string, stop: string, tomorrow: boolean): number {
          // Duration calculation for any substance.
          const startTime = this.temporisCalculum(start, false);
          const stopTime = this.temporisCalculum(stop, tomorrow);
          const duration = (stopTime - startTime) / (1000 * 60);

          console.log(`Simple duration calculation. Start is ${start} and stop is ${stop}. Tomorrow is ${tomorrow ? 'true' : 'false'}. Duration is ${duration}.`);

          return duration;
        },

        /**
         * Calculates infusion/injection duration for hydration administration while accounting for overlapping medication administration at the same site
         * @param start - Start time in HH:MM format (24-hour clock)
         * @param stop - End time in HH:MM format (24-hour clock)
         * @param tomorrow - Indicates if stop time crosses midnight
         * @param site - Treatment site for filtering records
         * @param offset - Current index in InfusionsInjections array
         * @returns Duration in minutes (including recursive adjustments)
         * @note Recursive implementation processes records sequentially
         * @complexity O(n) where n = InfusionsInjections.length
         * @sideeffect Logs calculation steps to console
         * @example
         * // Returns 120 for 10:00-12:00 session with no overlaps
         * iniCalculateDuration("10:00", "12:00", false, "Antecubital Right", 0);
         */
        iniCalculateDuration(start: string, stop: string, tomorrow: boolean, site: string, offset: number): number {
          // Duration calculation specific to theraputic hydration.
          let duration: number = 0;

          if (offset >= this.InfusionsInjections.length) {
            
            duration = this.iniCalculateDurationSimple(start, stop, tomorrow);
            console.log(`Final duration for this segment: ${duration}.`);

          } else {

            console.log(`Calculating duration using recursive function. Offset is ${offset}.`);
            console.log(`Start is ${start} and stop is ${stop}. Tomorrow is ${tomorrow ? 'true' : 'false'}.`);
            const comapre = this.InfusionsInjections[offset]; // Record we compare with

            // Our aim is to deduct time from a "theraputic hydration" entry but deduction can only be done using time frames from other types.
            if (comapre.type != "Theraputic Hydration" && comapre.site == site) {
              console.log("Comparing duration:")

              // Prepare submitted hydration entry.
              const nosInceptum = this.temporisCalculum(start, false);
              const nosTerminus = this.temporisCalculum(stop, tomorrow);

              // Prepared compared medication or chemotherapy entry.
              const vosInceptum = this.temporisCalculum(comapre.start, false);
              const vosTerminus = this.temporisCalculum(comapre.stop, comapre.nextDay);

              console.log(`Nos Inceptum: ${nosInceptum}`);
              console.log(`Nos Terminus: ${nosTerminus}`);
              console.log(`Vos Inceptum: ${vosInceptum}`);
              console.log(`Vos Terminus: ${vosTerminus}`);

              if (vosInceptum <= nosInceptum && nosTerminus <= vosTerminus) {

                // Nosology is completely within Vosology.
                console.log("Nosology is completely within Vosology.")
                console.log(`Ranges: ${start}-${stop} versus ${comapre.start}-${comapre.stop}.`)
                duration = 0;

              } else if (nosInceptum < vosInceptum && vosTerminus < nosTerminus) {

                // Vosology is completely within Nosology.
                console.log("Vosology is completely within Nosology.")
                console.log(`Ranges: ${comapre.start}-${comapre.stop} versus ${start}-${stop}.`)
                duration = this.iniCalculateDuration(start, comapre.start, false, site, offset + 1) + this.iniCalculateDuration(comapre.stop, stop, tomorrow, site, offset + 1);

              } else if (vosInceptum > nosInceptum && vosTerminus >= nosTerminus && nosTerminus > vosInceptum) {

                // Nosology overlaps Vosology.
                console.log("Nosology overlaps Vosology.")
                console.log(`Ranges: ${start}-${stop} versus ${comapre.start}-${comapre.stop}.`)
                duration = this.iniCalculateDuration(start, comapre.start, false, site, offset + 1);

              } else if (nosInceptum >= vosInceptum && nosTerminus > vosTerminus && vosTerminus > nosInceptum) {
                  
                // Vosology overlaps Nosology.
                console.log("Vosology overlaps Nosology.")
                console.log(`Ranges: ${comapre.start}-${comapre.stop} versus ${start}-${stop}.`)
                duration = this.iniCalculateDuration(comapre.stop, stop, tomorrow, site, offset + 1);
                  
              } else {

                // No medication administration occurs during the submitted time frame.
                console.log("No 'interception.' Moving to next iteration.")
                duration = this.iniCalculateDuration(start, stop, tomorrow, site, offset + 1);

              }
            } else {

              // Comparison record is theraputic hydration. We move to the next record.
              console.log("Type is 'theraputic hydration'. Moving to next iteration.")
              duration = this.iniCalculateDuration(start, stop, tomorrow, site, offset + 1);

            }
          }
          console.log(`Returning duration: ${duration}.`);
          return duration;
        },

        /**
         * Processes infusion/injection records to apply coding rules and determine initial procedures
         * @description  
         * Orchestrates the complete infusion coding workflow:
         * 1. Resets all initialCode flags
         * 2. Calculates durations based on therapy type
         * 3. Applies matching coding rules from codex store
         * 4. Determines highest priority procedures per treatment site
         * @sideeffects
         * - Modifies InfusionsInjections array properties:
         *   - procedures
         *   - charges
         *   - initialCode
         * - Accesses codexStore.iniRules for business logic
         * - Maintains treatment site priority groupings
         * @uses
         * - Uses iniCalculateDuration for therapeutic hydration timing
         * - Uses iniCalculateDurationSimple for other therapy types
         * - Relies on codexStore.iniRules configuration
         * @example
         * // Typical usage in component:
         * class InfusionCalculator {
         *   InfusionsInjections = [...];
         *   
         *   calculate() {
         *     this.iniCalculate();
         *   }
         * }
         * @see {@link iniRule} for rule configuration structure
         * @see {@link iniCalculateDuration} for complex duration logic
         * @see {@link iniCalculateDurationSimple} for basic timing
         */
        iniCalculate(): void {
          const codexStore = useCodexStore();
          const rules: IniRule[] = codexStore.iniRules as IniRule[];

          // Reset all initialCode variables to false
          this.InfusionsInjections.forEach(infusion => {
            infusion.initialCode = false;
          });

          const sitePriorities: { arrayIndex: number; site: string; priority: number }[] = [];

          for (let index = 0; index < this.InfusionsInjections.length; index++) {
            const record = this.InfusionsInjections[index];

            // Declare variable for storing previous procedure codes (including modifiers and quantity)
            let previousProcedures: { procedure: number; quantity: number | null; modifiers?: string[] }[] | null = [];

            // Calculate duration
            if (record.type == "Theraputic Hydration") {
              record.duration = this.iniCalculateDuration(record.start, record.stop, record.nextDay, record.site, 0);
              record.durationActual = this.iniCalculateDurationSimple(record.start, record.stop, record.nextDay);
            } else {
              record.durationActual = record.duration = this.iniCalculateDurationSimple(record.start, record.stop, record.nextDay);
            }

            // Determine drug count
            const drugCount = record.drugs?.length || 0;

            // Find matching rule
            const matchingRule = rules.find(rule => {
              const siteMatch = rule.input.sites === null || (Array.isArray(rule.input.sites) && rule.input.sites.includes(record.site));
              const durMin = rule.input.duration?.min ?? 0;
              const durMax = rule.input.duration?.max ?? Infinity;
              const dcMin = rule.input.drugCount?.min ?? 0;
              const dcMax = rule.input.drugCount?.max ?? Infinity;

              return (
                rule.input.type === record.type &&
                record.duration >= durMin &&
                record.duration <= durMax &&
                drugCount >= dcMin &&
                drugCount <= dcMax &&
                siteMatch
              );
            });

            // Apply rule outcome
            if (matchingRule) {
              // Store previous procedures before eventually erasing infusion and injection procedure codes of the encounter data
              previousProcedures = record.procedures;
              // Declare variable for storing previous modifiers
              let previousModifiers: string[] = [];
              record.procedures = matchingRule.output.procedures?.map(proc => {
                let calculatedQuantity: number | null = null;
                if (proc.quantity !== undefined) {
                  if (typeof proc.quantity === 'object') {
                    const { duration: base, factor } = proc.quantity;
                    calculatedQuantity = Math.ceil((record.duration - base) / factor) + 1;
                  } else {
                    calculatedQuantity = proc.quantity;
                  }
                }
                if (previousProcedures) {
                  // Loop through previous procedure codes to see if they match the newly generated ones
                  for (let idx = 0; idx < previousProcedures.length; idx++) {
                    if (previousProcedures[idx].procedure === proc.procedure) {
                      // If procedure codes match collect the modifiers associated with that procedure code
                      previousModifiers = previousProcedures[idx].modifiers ?? [];
                      break;
                    }
                  }
                }
                return {
                  procedure: proc.procedure,
                  quantity: calculatedQuantity,
                  modifiers: previousModifiers
                };
              }) ?? null;
              record.charges = matchingRule.output.charges;

              // Track priority and site
              sitePriorities.push({
                arrayIndex: index,
                site: record.site,
                priority: matchingRule.priority
              });
            } else {
              record.procedures = null;
              record.charges = false;
            }
          }

          // Group by site and determine highest priority
          const groups = sitePriorities.reduce((collection, item) => {
            const key = item.site;
            if (!collection[key]) collection[key] = [];
            collection[key].push(item);
            return collection;
          }, {} as Record<string, typeof sitePriorities>);

          // Set initialCode based on highest priority per site
          Object.values(groups).forEach(group => {
            group.sort((a, b) => {
              if (b.priority !== a.priority) return b.priority - a.priority;
              return a.arrayIndex - b.arrayIndex; // Earlier index first if priorities tie
            });
            const highest = group[0];
            this.InfusionsInjections[highest.arrayIndex].initialCode = true;
          });
        },

        removeInfusionInjection() {
          //Remove marked I&I entries.
          for (let i = this.InfusionsInjections.length - 1; i >= 0; i--) {
            if (this.InfusionsInjections[i].selected == true) {
              this.InfusionsInjections.splice(i, 1);
            }
          }
          this.iniCalculate();
          return this.InfusionsInjections;
        },

        /**
         * Removes selected procedure codes from the facility procedure codes array in the encounter store.
         * (Pinia action) Iterates backwards through procedureCodesFacility to safely remove selected items
         * while maintaining index integrity. Triggers association updates and returns the modified array.
         * 
         * @memberof useEncounterStore
         * @returns {Array<ProcedureCodeFacility>} Updated array of facility procedure codes
         * 
         * @example
         * // In Vue component setup:
         * const encounterStore = useEncounterStore();
         * const updatedProcedures = encounterStore.removeProcedureFacility();
         * 
         * @see {@link useEncounterStore.updateAssociations}
         * @note This Pinia store action directly mutates procedureCodesFacility state.
         *       All state modifications should occur through store actions like this one.
         */
        removeProcedureFacility() {
          //Remove marked procedure codes.
          for (let i = this.procedureCodesFacility.length - 1; i >= 0; i--) {
            if (this.procedureCodesFacility[i].selected == true) {
              this.procedureCodesFacility.splice(i, 1);
            }
          }
          this.updateAssociations();
          return this.procedureCodesFacility;
        },

        /**
         * Removes selected procedure codes from the professional procedure codes array in the encounter store.
         * (Pinia action) Iterates backwards through procedureCodesProfessional to safely remove selected items
         * while maintaining index integrity. Triggers association updates and returns the modified array.
         * 
         * @memberof useEncounterStore
         * @returns {Array<ProcedureCodeProfessional>} Updated array of professional procedure codes
         * 
         * @example
         * // In Vue component setup:
         * const encounterStore = useEncounterStore();
         * const updatedProcedures = encounterStore.removeProcedureProfessional();
         * 
         * @see {@link useEncounterStore.updateAssociations}
         * @note This Pinia store action directly mutates procedureCodesProfessional state.
         *       All state modifications should occur through store actions like this one.
         */
        removeProcedureProfessional() {
          //Remove marked procedure codes.
          for (let i = this.procedureCodesProfessional.length - 1; i >= 0; i--) {
            if (this.procedureCodesProfessional[i].selected == true) {
              this.procedureCodesProfessional.splice(i, 1);
            }
          }

          this.updateAssociations();
          return this.procedureCodesProfessional;
        },

        removeDiagnosis() {
          //Remove marked diagnosis codes.
          for (let i = this.diagnosisCodes.length - 1; i >= 0; i--) {
            if (this.diagnosisCodes[i].selected == true) {
              this.diagnosisCodes.splice(i, 1);
            }
          }
          //Reprioritise codes.
          for (let i = 0; i < this.diagnosisCodes.length; i++) {
            this.diagnosisCodes[i].priority = i
          }

          this.updateAssociations();
          return this.diagnosisCodes;
        },

        updateAssociations() {
          //Forward Mapping
          for (let i = 0; i < this.diagnosisCodes.length; i++) {
            this.diagnosisCodes[i].cpt = [];
            //Types: facility, professional

            for (let j = 0; j < this.procedureCodesFacility.length; j++) {
              if (this.procedureCodesFacility[j].diagnosis == this.diagnosisCodes[i].code) {
                this.diagnosisCodes[i].cpt.push({ code: this.procedureCodesFacility[j].code, category: "facility"})
              }
            }

            for (let k = 0; k < this.procedureCodesProfessional.length; k++) {
              if (this.procedureCodesProfessional[k].diagnosis == this.diagnosisCodes[i].code) {
                this.diagnosisCodes[i].cpt.push({ code: this.procedureCodesFacility[k].code, category: "professional"})
              }
            }
          }

          //Reverse Mapping
          for (let j = 0; j < this.procedureCodesFacility.length; j++) {
            if (this.diagnosisCodes.find(o => o.code === this.procedureCodesFacility[j].diagnosis) == null ){
              this.procedureCodesFacility[j].diagnosis = null;
            }
          }
          for (let k = 0; k < this.procedureCodesProfessional.length; k++) {
            if (this.diagnosisCodes.find(o => o.code === this.procedureCodesProfessional[k].diagnosis) == null ){
              this.procedureCodesProfessional[k].diagnosis = null;
            }
          }

        }
      },
})