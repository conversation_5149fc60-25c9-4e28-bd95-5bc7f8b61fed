{"name": "expericoderminimalist", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@aws-amplify/ui-vue": "^3.1.14", "@quasar/extras": "^1.17.0", "apexcharts": "^4.7.0", "aws-amplify": "^5.1.3", "axios": "^1.3.5", "pinia": "^2.2.1", "quasar": "^2.18.1", "vue": "^3.5.15", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}, "devDependencies": {"@quasar/vite-plugin": "^1.9.0", "@rushstack/eslint-patch": "^1.2.0", "@types/node": "^18.14.2", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "sass": "^1.89.0", "typescript": "~4.8.4", "vite": "^6.3.5", "vue-tsc": "^1.2.0"}}