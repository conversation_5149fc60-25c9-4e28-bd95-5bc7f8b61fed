{"name": "expericoderminimalist", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@aws-amplify/ui-vue": "^3.1.14", "@quasar/extras": "^1.16.2", "apexcharts": "^3.39.0", "aws-amplify": "^5.1.3", "axios": "^1.3.5", "pinia": "^2.2.1", "quasar": "^2.11.10", "vue": "^3.2.47", "vue-router": "^4.1.6", "vue3-apexcharts": "^1.4.1"}, "devDependencies": {"@quasar/vite-plugin": "^1.3.0", "@rushstack/eslint-patch": "^1.2.0", "@types/node": "^18.14.2", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "sass": "^1.32.12", "typescript": "~4.8.4", "vite": "^4.1.4", "vue-tsc": "^1.2.0"}}