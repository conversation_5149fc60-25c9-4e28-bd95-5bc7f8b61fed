version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - pip install --upgrade pip
      - pip install --upgrade awscli
  pre_build:
    commands:
      - echo Pre_build Phase
      - npm install
  build:
    commands:
      - echo Build Phase
      - npm run build
  post_build:
    commands:
      - echo PostBuild Phase
      - aws s3 sync ./dist $S3_BUCKET
      - aws cloudfront create-invalidation --distribution-id=E343FNPU2MQWSB --paths '/*'
